import json

json_data = """
{
  "text": "Found 7 items in '/zhengju/6.1.1a'",
  "files": [],
  "json": [
    {
      "items": [
        {
          "name": "6.1.1a",
          "path": "6.1.1a/",
          "type": "directory"
        },
        {
          "name": "0.docx",
          "path": "0.docx",
          "type": "file"
        },
        {
          "name": "1.docx",
          "path": "1.docx",
          "type": "file"
        },
        {
          "name": "2.docx",
          "path": "2.docx",
          "type": "file"
        }
      ],
      "path": "/zhengju/6.1.1a",
      "total_count": 3
    }
  ]
}
"""

data = json.loads(json_data)

filenames = [
    item["name"]
    for entry in data.get("json", [])
    for item in entry.get("items", [])
    if item.get("type") == "file"
]

print("\n".join(filenames))
