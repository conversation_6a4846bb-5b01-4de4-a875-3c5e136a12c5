const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Tech Tutorial Hub - Environment Check\n');

// Check Node.js version
try {
  const nodeVersion = process.version;
  console.log(`✓ Node.js version: ${nodeVersion}`);
  
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  if (majorVersion < 18) {
    console.log('⚠️  Warning: Node.js 18+ is recommended');
  }
} catch (error) {
  console.log('✗ Node.js check failed');
  process.exit(1);
}

// Check npm version
try {
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  console.log(`✓ npm version: ${npmVersion}`);
} catch (error) {
  console.log('✗ npm not found');
  process.exit(1);
}

// Check if we're on Windows
const isWindows = process.platform === 'win32';
console.log(`✓ Platform: ${process.platform} ${isWindows ? '(Windows detected)' : ''}`);

// Check project structure
const requiredFiles = [
  'package.json',
  'next.config.ts',
  'tsconfig.json',
  'src/app/layout.tsx',
  'src/app/page.tsx'
];

console.log('\n📁 Checking project structure:');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✓ ${file}`);
  } else {
    console.log(`✗ ${file} (missing)`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing!');
  process.exit(1);
}

// Check dependencies
console.log('\n📦 Checking dependencies:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  const criticalDeps = ['next', 'react', 'react-dom', 'typescript'];
  
  criticalDeps.forEach(dep => {
    if (deps[dep]) {
      console.log(`✓ ${dep}: ${deps[dep]}`);
    } else {
      console.log(`✗ ${dep} (missing)`);
    }
  });
} catch (error) {
  console.log('✗ Error reading package.json');
  process.exit(1);
}

// Check if node_modules exists
if (fs.existsSync('node_modules')) {
  console.log('✓ node_modules directory exists');
  
  // Check if Next.js is properly installed
  const nextBin = isWindows ? 'node_modules\\.bin\\next.cmd' : 'node_modules/.bin/next';
  if (fs.existsSync(nextBin)) {
    console.log('✓ Next.js binary found');
  } else {
    console.log('✗ Next.js binary not found - run npm install');
  }
} else {
  console.log('⚠️  node_modules not found - run npm install first');
}

console.log('\n🎉 Environment check completed!');

if (isWindows) {
  console.log('\n💡 Windows-specific tips:');
  console.log('   • Use "npm run dev:windows" for better memory handling');
  console.log('   • Run PowerShell as Administrator if you encounter permission issues');
  console.log('   • Consider using Windows Terminal for better experience');
}
