import unittest
import sys
import os
import io
import json
from unittest.mock import patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# 导入要测试的模块
import extract_files

class TestExtractFiles(unittest.TestCase):
    """测试extract_files.py的功能"""
    
    def test_json_string_processing(self):
        """测试JSON字符串处理功能"""
        # 测试单引号到双引号的转换
        original_string = extract_files.data['output']
        json_string = original_string.replace("'", "\"")
        
        # 验证转换后的字符串可以被JSON解析
        try:
            parsed_data = json.loads(json_string)
            self.assertIsInstance(parsed_data, dict)
        except json.JSONDecodeError:
            self.fail("转换后的字符串无法被JSON解析")
    
    def test_file_extraction(self):
        """测试文件名提取功能"""
        with patch('sys.stdout', new=io.StringIO()) as fake_stdout:
            # 重新执行模块中的代码逻辑
            json_string = extract_files.data['output'].replace("'", "\"")
            output_data = json.loads(json_string)
            items = output_data['json'][0]['items']
            file_names = [item['name'] for item in items if item['type'] == 'file']
            
            # 预期的文件名
            expected_files = ["1.docx", "2.docx"]
            
            # 验证提取的文件名
            self.assertEqual(set(file_names), set(expected_files))
    
    def test_data_structure(self):
        """测试数据结构的正确性"""
        # 验证原始数据结构
        self.assertIsInstance(extract_files.data, dict)
        self.assertIn('output', extract_files.data)
        
        # 验证解析后的数据结构
        json_string = extract_files.data['output'].replace("'", "\"")
        output_data = json.loads(json_string)
        
        self.assertIn('json', output_data)
        self.assertIsInstance(output_data['json'], list)
        self.assertGreater(len(output_data['json']), 0)
        
        # 验证items结构
        items = output_data['json'][0]['items']
        self.assertIsInstance(items, list)
        
        for item in items:
            self.assertIn('name', item)
            self.assertIn('type', item)
            self.assertIn('path', item)
    
    def test_file_filtering(self):
        """测试文件过滤功能"""
        json_string = extract_files.data['output'].replace("'", "\"")
        output_data = json.loads(json_string)
        items = output_data['json'][0]['items']
        
        # 分别统计文件和目录
        files = [item for item in items if item['type'] == 'file']
        directories = [item for item in items if item['type'] == 'directory']
        
        # 验证过滤结果
        self.assertGreater(len(files), 0, "应该至少有一个文件")
        self.assertGreater(len(directories), 0, "应该至少有一个目录")
        
        # 验证所有文件都有正确的类型
        for file_item in files:
            self.assertEqual(file_item['type'], 'file')

if __name__ == '__main__':
    unittest.main()
