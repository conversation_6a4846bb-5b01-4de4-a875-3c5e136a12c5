"use client"

import { useState, useEffect } from "react"
import { SearchDialog } from "./search-dialog"

interface SearchTriggerProps {
  className?: string
  placeholder?: string
}

export function SearchTrigger({ className = "", placeholder = "搜索教程..." }: SearchTriggerProps) {
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault()
        setIsOpen(true)
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [])

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className={`relative flex items-center w-full text-left ${className}`}
      >
        <div className="flex items-center space-x-3 w-full">
          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <span className="text-gray-600 dark:text-gray-400 flex-1">{placeholder}</span>
          <div className="hidden sm:flex items-center space-x-1">
            <kbd className="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
              Ctrl+K
            </kbd>
          </div>
        </div>
      </button>

      <SearchDialog isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  )
}
