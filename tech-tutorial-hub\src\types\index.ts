export interface PostMeta {
  title: string
  description: string
  date: string
  published: boolean
  tags: string[]
  category: string
  author: string
  image?: string
  slug: string
  readingTime: number
}

export interface NavigationItem {
  title: string
  href?: string
  items?: NavigationItem[]
}

export interface SiteConfig {
  name: string
  description: string
  url: string
  ogImage: string
  author: string
  keywords: string[]
  links: {
    github: string
  }
}

export interface AdPlacement {
  id: string
  type: 'banner' | 'rectangle' | 'sidebar' | 'inline'
  size: string
  position: 'header' | 'sidebar' | 'content' | 'footer'
}
