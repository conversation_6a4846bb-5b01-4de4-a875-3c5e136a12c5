import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { remark } from 'remark'
import remarkHtml from 'remark-html'
import remarkGfm from 'remark-gfm'
import { getAllPosts as getContentPosts, getPostBySlug as getContentPost } from './content-manager'

const contentDirectory = path.join(process.cwd(), 'content')

export interface PostMeta {
  title: string
  description: string
  date: string
  published: boolean
  tags: string[]
  category: string
  author: string
  image?: string
  slug: string
  readingTime: number
}

export async function getPostBySlug(slug: string) {
  const post = getContentPost(slug)

  if (!post) {
    return null
  }

  // Process markdown content
  const processedContent = await remark()
    .use(remarkGfm)
    .use(remarkHtml, { sanitize: false })
    .process(post.content)

  const htmlContent = processedContent.toString()

  return {
    meta: {
      title: post.metadata.title,
      description: post.metadata.description,
      date: post.metadata.date,
      published: !post.metadata.draft,
      tags: post.metadata.tags || [],
      category: post.metadata.category,
      author: post.metadata.author,
      slug: post.slug,
      readingTime: post.readingTime,
    } as PostMeta,
    content: htmlContent,
    rawContent: post.content,
  }
}

export function getAllPosts(): PostMeta[] {
  const posts = getContentPosts()

  return posts
    .filter(post => !post.metadata.draft)
    .map(post => ({
      title: post.metadata.title,
      description: post.metadata.description,
      date: post.metadata.date,
      published: !post.metadata.draft,
      tags: post.metadata.tags || [],
      category: post.metadata.category,
      author: post.metadata.author,
      slug: post.slug,
      readingTime: post.readingTime,
    } as PostMeta))
}

export function getPostsByCategory(category: string): PostMeta[] {
  return getAllPosts().filter(post => post.category === category)
}

export function getAllCategories(): string[] {
  const posts = getAllPosts()
  const categories = posts.map(post => post.category)
  return Array.from(new Set(categories))
}

export function getAllTags(): string[] {
  const posts = getAllPosts()
  const tags = posts.flatMap(post => post.tags)
  return Array.from(new Set(tags))
}
