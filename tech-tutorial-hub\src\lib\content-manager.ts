// Simplified content manager for development
// Types
export interface PostMetadata {
  title: string
  description: string
  date: string
  category: string
  tags?: string[]
  author?: string
  readingTime?: number
  difficulty?: 'beginner' | 'intermediate' | 'advanced'
  featured?: boolean
  draft?: boolean
}

export interface Post {
  slug: string
  metadata: PostMetadata
  content: string
  excerpt: string
}

// Mock data for development
const mockPosts: Post[] = [
  {
    slug: 'react-hooks-guide',
    metadata: {
      title: 'React Hooks 完全指南',
      description: '深入了解 React Hooks 的使用方法和最佳实践',
      date: '2024-01-15',
      category: 'React',
      tags: ['React', 'Hooks', 'JavaScript'],
      author: 'Tech Tutorial Hub',
      readingTime: 15,
      difficulty: 'intermediate',
      featured: true,
      draft: false
    },
    content: '这是一篇关于 React Hooks 的详细教程...',
    excerpt: '深入了解 React Hooks 的使用方法和最佳实践，包括 useState、useEffect 等核心 Hooks。'
  },
  {
    slug: 'nextjs-performance',
    metadata: {
      title: 'Next.js 性能优化指南',
      description: '学习如何优化 Next.js 应用的性能',
      date: '2024-01-10',
      category: 'Next.js',
      tags: ['Next.js', 'Performance', 'Optimization'],
      author: 'Tech Tutorial Hub',
      readingTime: 12,
      difficulty: 'advanced',
      featured: true,
      draft: false
    },
    content: '这是一篇关于 Next.js 性能优化的教程...',
    excerpt: '学习如何通过各种技术手段优化 Next.js 应用的性能，提升用户体验。'
  },
  {
    slug: 'typescript-basics',
    metadata: {
      title: 'TypeScript 基础教程',
      description: 'TypeScript 入门指南，从基础到进阶',
      date: '2024-01-05',
      category: 'TypeScript',
      tags: ['TypeScript', 'JavaScript', 'Types'],
      author: 'Tech Tutorial Hub',
      readingTime: 10,
      difficulty: 'beginner',
      featured: false,
      draft: false
    },
    content: '这是一篇关于 TypeScript 基础的教程...',
    excerpt: 'TypeScript 入门指南，学习类型系统、接口、泛型等核心概念。'
  },
  {
    slug: 'css-grid-layout',
    metadata: {
      title: 'CSS Grid 布局完全指南',
      description: '掌握 CSS Grid 布局的核心概念和实用技巧',
      date: '2024-01-01',
      category: 'CSS',
      tags: ['CSS', 'Grid', 'Layout'],
      author: 'Tech Tutorial Hub',
      readingTime: 8,
      difficulty: 'intermediate',
      featured: false,
      draft: false
    },
    content: '这是一篇关于 CSS Grid 布局的教程...',
    excerpt: '掌握 CSS Grid 布局的核心概念和实用技巧，创建复杂的网页布局。'
  }
]

// Get all posts
export function getAllPosts(): Post[] {
  return mockPosts.sort((a, b) => new Date(b.metadata.date).getTime() - new Date(a.metadata.date).getTime())
}

// Get post by slug
export function getPostBySlug(slug: string): Post | null {
  return mockPosts.find(post => post.slug === slug) || null
}

// Get featured posts
export function getFeaturedPosts(limit = 3): Post[] {
  return getAllPosts()
    .filter(post => post.metadata.featured)
    .slice(0, limit)
}

// Get recent posts
export function getRecentPosts(limit = 5): Post[] {
  return getAllPosts().slice(0, limit)
}

// Get related posts
export function getRelatedPosts(currentPost: Post, limit = 3): Post[] {
  const allPosts = getAllPosts().filter(post => post.slug !== currentPost.slug)
  
  // Score posts based on similarity
  const scoredPosts = allPosts.map(post => {
    let score = 0
    
    // Same category gets high score
    if (post.metadata.category === currentPost.metadata.category) {
      score += 10
    }
    
    // Shared tags get medium score
    const sharedTags = post.metadata.tags?.filter(tag =>
      currentPost.metadata.tags?.includes(tag)
    ) || []
    score += sharedTags.length * 5
    
    // Same difficulty level gets small score
    if (post.metadata.difficulty === currentPost.metadata.difficulty) {
      score += 2
    }
    
    return { post, score }
  })
  
  return scoredPosts
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.post)
}

// Get all categories
export function getAllCategories(): { name: string; count: number }[] {
  const posts = getAllPosts()
  const categoryCount: Record<string, number> = {}
  
  posts.forEach(post => {
    const category = post.metadata.category
    categoryCount[category] = (categoryCount[category] || 0) + 1
  })
  
  return Object.entries(categoryCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
}

// Get all tags
export function getAllTags(): { name: string; count: number }[] {
  const posts = getAllPosts()
  const tagCount: Record<string, number> = {}
  
  posts.forEach(post => {
    post.metadata.tags?.forEach(tag => {
      tagCount[tag] = (tagCount[tag] || 0) + 1
    })
  })
  
  return Object.entries(tagCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
}

// Search posts
export function searchPosts(query: string): Post[] {
  const lowercaseQuery = query.toLowerCase()
  return getAllPosts().filter(post => 
    post.metadata.title.toLowerCase().includes(lowercaseQuery) ||
    post.metadata.description.toLowerCase().includes(lowercaseQuery) ||
    post.metadata.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    post.metadata.category.toLowerCase().includes(lowercaseQuery)
  )
}

// Get posts by category
export function getPostsByCategory(category: string): Post[] {
  return getAllPosts().filter(post => post.metadata.category === category)
}

// Get posts by tag
export function getPostsByTag(tag: string): Post[] {
  return getAllPosts().filter(post => post.metadata.tags?.includes(tag))
}
