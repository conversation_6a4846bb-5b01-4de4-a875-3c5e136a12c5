import { trackEvent } from './analytics'

// 用户行为跟踪类
export class UserBehaviorTracker {
  private startTime: number
  private scrollDepth: number = 0
  private maxScrollDepth: number = 0
  private readingTime: number = 0
  private isActive: boolean = true
  private lastActivityTime: number
  private engagementEvents: Array<{ type: string; timestamp: number; data?: any }> = []

  constructor() {
    this.startTime = Date.now()
    this.lastActivityTime = Date.now()
    this.initializeTracking()
  }

  private initializeTracking() {
    if (typeof window === 'undefined') return

    // 滚动深度跟踪
    this.trackScrollDepth()
    
    // 阅读时间跟踪
    this.trackReadingTime()
    
    // 用户活动跟踪
    this.trackUserActivity()
    
    // 页面离开跟踪
    this.trackPageExit()
    
    // 点击跟踪
    this.trackClicks()
    
    // 表单交互跟踪
    this.trackFormInteractions()
  }

  // 滚动深度跟踪
  private trackScrollDepth() {
    let ticking = false

    const updateScrollDepth = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollPercent = Math.round((scrollTop / docHeight) * 100)

      this.scrollDepth = scrollPercent
      this.maxScrollDepth = Math.max(this.maxScrollDepth, scrollPercent)

      // 发送滚动里程碑事件
      if (scrollPercent >= 25 && !this.hasReachedMilestone(25)) {
        this.recordEngagementEvent('scroll_25', { depth: 25 })
        trackEvent.engagement('scroll', 'milestone_25')
      }
      if (scrollPercent >= 50 && !this.hasReachedMilestone(50)) {
        this.recordEngagementEvent('scroll_50', { depth: 50 })
        trackEvent.engagement('scroll', 'milestone_50')
      }
      if (scrollPercent >= 75 && !this.hasReachedMilestone(75)) {
        this.recordEngagementEvent('scroll_75', { depth: 75 })
        trackEvent.engagement('scroll', 'milestone_75')
      }
      if (scrollPercent >= 90 && !this.hasReachedMilestone(90)) {
        this.recordEngagementEvent('scroll_90', { depth: 90 })
        trackEvent.engagement('scroll', 'milestone_90')
      }

      ticking = false
    }

    const onScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollDepth)
        ticking = true
      }
    }

    window.addEventListener('scroll', onScroll, { passive: true })
  }

  // 阅读时间跟踪
  private trackReadingTime() {
    setInterval(() => {
      if (this.isActive) {
        this.readingTime += 1
        
        // 每30秒发送一次阅读时间更新
        if (this.readingTime % 30 === 0) {
          trackEvent.engagement('reading', 'time_update', this.readingTime)
        }
      }
    }, 1000)
  }

  // 用户活动跟踪
  private trackUserActivity() {
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    
    const updateActivity = () => {
      this.isActive = true
      this.lastActivityTime = Date.now()
    }

    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true })
    })

    // 检查用户是否不活跃
    setInterval(() => {
      const timeSinceLastActivity = Date.now() - this.lastActivityTime
      if (timeSinceLastActivity > 30000) { // 30秒无活动
        this.isActive = false
      }
    }, 5000)
  }

  // 页面离开跟踪
  private trackPageExit() {
    const sendExitData = () => {
      const sessionData = {
        duration: Date.now() - this.startTime,
        maxScrollDepth: this.maxScrollDepth,
        readingTime: this.readingTime,
        engagementEvents: this.engagementEvents.length,
        url: window.location.href
      }

      trackEvent.event({
        action: 'page_exit',
        category: 'engagement',
        label: 'session_end',
        value: sessionData.duration,
        custom_parameters: sessionData
      })

      // 使用 sendBeacon 确保数据发送
      if (navigator.sendBeacon) {
        const data = JSON.stringify({
          event: 'page_exit',
          ...sessionData
        })
        navigator.sendBeacon('/api/analytics', data)
      }
    }

    window.addEventListener('beforeunload', sendExitData)
    window.addEventListener('pagehide', sendExitData)
    
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        sendExitData()
      }
    })
  }

  // 点击跟踪
  private trackClicks() {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      
      // 跟踪链接点击
      if (target.tagName === 'A') {
        const href = target.getAttribute('href')
        const text = target.textContent?.trim()
        
        if (href) {
          const isExternal = href.startsWith('http') && !href.includes(window.location.hostname)
          
          if (isExternal) {
            trackEvent.externalLink(href, text || '')
          } else {
            trackEvent.navigation(window.location.pathname, href)
          }
        }
      }
      
      // 跟踪按钮点击
      if (target.tagName === 'BUTTON' || target.getAttribute('role') === 'button') {
        const buttonText = target.textContent?.trim() || target.getAttribute('aria-label') || 'unknown'
        trackEvent.engagement('click', 'button', 0)
        
        this.recordEngagementEvent('button_click', {
          text: buttonText,
          className: target.className
        })
      }
      
      // 跟踪代码块点击（复制功能）
      if (target.closest('pre') || target.closest('code')) {
        trackEvent.engagement('click', 'code_block')
        this.recordEngagementEvent('code_interaction', {
          type: 'click'
        })
      }
    })
  }

  // 表单交互跟踪
  private trackFormInteractions() {
    // 表单字段焦点
    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT') {
        trackEvent.engagement('focus', 'form_field')
        this.recordEngagementEvent('form_focus', {
          fieldType: target.tagName.toLowerCase(),
          fieldName: target.getAttribute('name') || 'unknown'
        })
      }
    })

    // 表单提交
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement
      const formId = form.id || form.className || 'unknown'
      
      trackEvent.engagement('submit', 'form')
      this.recordEngagementEvent('form_submit', {
        formId: formId
      })
    })
  }

  // 记录参与事件
  private recordEngagementEvent(type: string, data?: any) {
    this.engagementEvents.push({
      type,
      timestamp: Date.now(),
      data
    })
  }

  // 检查是否已达到滚动里程碑
  private hasReachedMilestone(milestone: number): boolean {
    return this.engagementEvents.some(event => 
      event.type === `scroll_${milestone}`
    )
  }

  // 获取当前会话统计
  public getSessionStats() {
    return {
      duration: Date.now() - this.startTime,
      readingTime: this.readingTime,
      scrollDepth: this.scrollDepth,
      maxScrollDepth: this.maxScrollDepth,
      engagementEvents: this.engagementEvents.length,
      isActive: this.isActive
    }
  }

  // 手动发送参与度报告
  public sendEngagementReport() {
    const stats = this.getSessionStats()
    
    trackEvent.event({
      action: 'engagement_report',
      category: 'user_behavior',
      label: 'manual_report',
      value: stats.duration,
      custom_parameters: {
        ...stats,
        url: window.location.href,
        timestamp: new Date().toISOString()
      }
    })
  }
}

// 全局用户行为跟踪器实例
let globalTracker: UserBehaviorTracker | null = null

// 初始化用户行为跟踪
export function initUserTracking() {
  if (typeof window === 'undefined' || globalTracker) return
  
  globalTracker = new UserBehaviorTracker()
  return globalTracker
}

// 获取全局跟踪器实例
export function getUserTracker(): UserBehaviorTracker | null {
  return globalTracker
}

// 特定内容跟踪函数
export const contentTracking = {
  // 文章阅读进度
  trackArticleProgress: (articleId: string, progress: number) => {
    trackEvent.event({
      action: 'article_progress',
      category: 'content',
      label: articleId,
      value: progress,
      custom_parameters: {
        article_id: articleId,
        progress_percent: progress
      }
    })
  },

  // 代码复制
  trackCodeCopy: (language: string, codeLength: number) => {
    trackEvent.codeCopy(language, 'x'.repeat(codeLength))
  },

  // 搜索使用
  trackSearch: (query: string, resultsCount: number, source: string) => {
    trackEvent.search(query, resultsCount)
    trackEvent.event({
      action: 'search_performed',
      category: 'content',
      label: source,
      value: resultsCount,
      custom_parameters: {
        search_query: query,
        search_source: source,
        results_count: resultsCount
      }
    })
  },

  // 分类浏览
  trackCategoryView: (category: string, postCount: number) => {
    trackEvent.event({
      action: 'category_view',
      category: 'content',
      label: category,
      value: postCount,
      custom_parameters: {
        category_name: category,
        posts_count: postCount
      }
    })
  }
}
