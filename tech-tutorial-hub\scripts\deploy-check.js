#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 部署前检查脚本
class DeploymentChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = [];
  }

  // 运行所有检查
  async runAllChecks() {
    console.log('🚀 开始部署前检查...\n');

    await this.checkEnvironmentVariables();
    await this.checkContentQuality();
    await this.checkRequiredPages();
    await this.checkSEOOptimization();
    await this.checkPerformance();
    await this.checkSecurity();
    await this.checkAccessibility();

    this.generateReport();
  }

  // 检查环境变量
  async checkEnvironmentVariables() {
    console.log('📋 检查环境变量...');

    const requiredEnvVars = [
      'NEXT_PUBLIC_SITE_URL',
      'NEXT_PUBLIC_GA_ID'
    ];

    const optionalEnvVars = [
      'GOOGLE_SITE_VERIFICATION',
      'YANDEX_VERIFICATION',
      'YAHOO_VERIFICATION'
    ];

    // 检查必需的环境变量
    requiredEnvVars.forEach(envVar => {
      if (!process.env[envVar]) {
        this.errors.push(`缺少必需的环境变量: ${envVar}`);
      } else {
        this.passed.push(`环境变量 ${envVar} 已设置`);
      }
    });

    // 检查可选的环境变量
    optionalEnvVars.forEach(envVar => {
      if (!process.env[envVar]) {
        this.warnings.push(`建议设置环境变量: ${envVar}`);
      } else {
        this.passed.push(`环境变量 ${envVar} 已设置`);
      }
    });
  }

  // 检查内容质量
  async checkContentQuality() {
    console.log('📝 检查内容质量...');

    try {
      // 检查content目录是否存在
      const contentDir = path.join(process.cwd(), 'content', 'posts');
      if (!fs.existsSync(contentDir)) {
        this.warnings.push('content/posts 目录不存在，请创建并添加文章');
        return;
      }

      // 检查文章文件
      const files = fs.readdirSync(contentDir).filter(file => file.endsWith('.md'));

      if (files.length < 20) {
        this.warnings.push(`文章数量不足，当前 ${files.length} 篇，建议至少 20 篇`);
      } else {
        this.passed.push(`文章数量充足: ${files.length} 篇`);
      }

      // 检查文章内容
      let validArticles = 0;
      files.forEach(file => {
        const filePath = path.join(contentDir, file);
        const content = fs.readFileSync(filePath, 'utf8');

        // 检查是否有frontmatter
        if (content.startsWith('---')) {
          validArticles++;
        }

        // 检查文章长度
        if (content.length < 1000) {
          this.warnings.push(`文章 "${file}" 内容较短，建议增加内容`);
        }
      });

      if (validArticles > 0) {
        this.passed.push(`有效文章数量: ${validArticles} 篇`);
      }

    } catch (error) {
      this.errors.push(`内容检查失败: ${error.message}`);
    }
  }

  // 检查必需页面
  async checkRequiredPages() {
    console.log('📄 检查必需页面...');

    const requiredPages = [
      { path: 'src/app/page.tsx', name: '首页' },
      { path: 'src/app/about/page.tsx', name: '关于页面' },
      { path: 'src/app/contact/page.tsx', name: '联系页面' },
      { path: 'src/app/privacy/page.tsx', name: '隐私政策' },
      { path: 'src/app/terms/page.tsx', name: '服务条款' },
      { path: 'src/app/tutorials/page.tsx', name: '教程页面' }
    ];

    requiredPages.forEach(page => {
      const fullPath = path.join(process.cwd(), page.path);
      if (fs.existsSync(fullPath)) {
        this.passed.push(`${page.name} 页面存在`);
      } else {
        this.errors.push(`缺少 ${page.name} 页面: ${page.path}`);
      }
    });
  }

  // 检查SEO优化
  async checkSEOOptimization() {
    console.log('🔍 检查SEO优化...');

    const seoFiles = [
      { path: 'src/app/sitemap.ts', name: 'XML网站地图' },
      { path: 'src/app/robots.ts', name: 'robots.txt' },
      { path: 'src/lib/seo.ts', name: 'SEO工具函数' }
    ];

    seoFiles.forEach(file => {
      const fullPath = path.join(process.cwd(), file.path);
      if (fs.existsSync(fullPath)) {
        this.passed.push(`${file.name} 已配置`);
      } else {
        this.errors.push(`缺少 ${file.name}: ${file.path}`);
      }
    });

    // 检查favicon和图标
    const iconFiles = [
      'public/favicon.ico',
      'public/favicon.svg',
      'public/apple-touch-icon.png',
      'public/site.webmanifest'
    ];

    iconFiles.forEach(iconFile => {
      const fullPath = path.join(process.cwd(), iconFile);
      if (fs.existsSync(fullPath)) {
        this.passed.push(`图标文件存在: ${iconFile}`);
      } else {
        this.warnings.push(`建议添加图标文件: ${iconFile}`);
      }
    });
  }

  // 检查性能配置
  async checkPerformance() {
    console.log('⚡ 检查性能配置...');

    const performanceFiles = [
      { path: 'src/lib/performance.ts', name: '性能监控' },
      { path: 'src/lib/analytics.ts', name: '分析工具' },
      { path: 'src/components/analytics/google-analytics.tsx', name: 'Google Analytics组件' }
    ];

    performanceFiles.forEach(file => {
      const fullPath = path.join(process.cwd(), file.path);
      if (fs.existsSync(fullPath)) {
        this.passed.push(`${file.name} 已配置`);
      } else {
        this.warnings.push(`建议配置 ${file.name}: ${file.path}`);
      }
    });

    // 检查Next.js配置
    const nextConfigPath = path.join(process.cwd(), 'next.config.js');
    if (fs.existsSync(nextConfigPath)) {
      this.passed.push('Next.js 配置文件存在');
      
      // 检查配置内容
      try {
        const configContent = fs.readFileSync(nextConfigPath, 'utf8');
        if (configContent.includes('compress')) {
          this.passed.push('启用了压缩优化');
        } else {
          this.warnings.push('建议在 next.config.js 中启用压缩');
        }
      } catch (error) {
        this.warnings.push('无法读取 Next.js 配置文件');
      }
    } else {
      this.warnings.push('建议添加 next.config.js 配置文件');
    }
  }

  // 检查安全配置
  async checkSecurity() {
    console.log('🔒 检查安全配置...');

    // 检查环境变量文件
    const envFiles = ['.env.example', '.env.local'];
    envFiles.forEach(envFile => {
      const fullPath = path.join(process.cwd(), envFile);
      if (fs.existsSync(fullPath)) {
        this.passed.push(`环境变量文件存在: ${envFile}`);
      } else if (envFile === '.env.example') {
        this.warnings.push(`建议添加 ${envFile} 作为模板`);
      }
    });

    // 检查.gitignore
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      this.passed.push('.gitignore 文件存在');
      
      try {
        const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
        if (gitignoreContent.includes('.env.local')) {
          this.passed.push('环境变量文件已被忽略');
        } else {
          this.warnings.push('建议在 .gitignore 中添加 .env.local');
        }
      } catch (error) {
        this.warnings.push('无法读取 .gitignore 文件');
      }
    } else {
      this.errors.push('缺少 .gitignore 文件');
    }
  }

  // 检查可访问性
  async checkAccessibility() {
    console.log('♿ 检查可访问性配置...');

    // 检查是否有可访问性相关的配置
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        // 检查是否有可访问性相关的依赖
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
        
        if (dependencies['@axe-core/react'] || dependencies['eslint-plugin-jsx-a11y']) {
          this.passed.push('已配置可访问性检查工具');
        } else {
          this.warnings.push('建议添加可访问性检查工具');
        }
        
      } catch (error) {
        this.warnings.push('无法读取 package.json 文件');
      }
    }

    this.passed.push('可访问性检查完成');
  }

  // 生成检查报告
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 部署检查报告');
    console.log('='.repeat(60));

    // 显示通过的检查
    if (this.passed.length > 0) {
      console.log('\n✅ 通过的检查:');
      this.passed.forEach(item => console.log(`   ✓ ${item}`));
    }

    // 显示警告
    if (this.warnings.length > 0) {
      console.log('\n⚠️  警告:');
      this.warnings.forEach(item => console.log(`   ⚠ ${item}`));
    }

    // 显示错误
    if (this.errors.length > 0) {
      console.log('\n❌ 错误:');
      this.errors.forEach(item => console.log(`   ✗ ${item}`));
    }

    // 总结
    console.log('\n' + '='.repeat(60));
    console.log('📈 检查总结:');
    console.log(`   通过: ${this.passed.length} 项`);
    console.log(`   警告: ${this.warnings.length} 项`);
    console.log(`   错误: ${this.errors.length} 项`);

    // 部署建议
    if (this.errors.length === 0) {
      if (this.warnings.length === 0) {
        console.log('\n🎉 恭喜！所有检查都通过了，可以安全部署！');
      } else {
        console.log('\n✅ 可以部署，但建议先解决警告项以获得更好的效果。');
      }
    } else {
      console.log('\n🚫 请先解决所有错误项再进行部署。');
      process.exit(1);
    }

    console.log('='.repeat(60));
  }
}

// 运行检查
const checker = new DeploymentChecker();
checker.runAllChecks().catch(error => {
  console.error('检查过程中发生错误:', error);
  process.exit(1);
});
