@tailwind base;
@tailwind components;
@tailwind utilities;

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap');

@layer base {
  :root {
    /* Google Color Palette */
    --google-blue: #1a73e8;
    --google-blue-hover: #1557b0;
    --google-green: #34a853;
    --google-yellow: #fbbc04;
    --google-red: #ea4335;
    --google-gray-50: #f8f9fa;
    --google-gray-100: #f1f3f4;
    --google-gray-200: #e8eaed;
    --google-gray-300: #dadce0;
    --google-gray-400: #bdc1c6;
    --google-gray-500: #9aa0a6;
    --google-gray-600: #80868b;
    --google-gray-700: #5f6368;
    --google-gray-800: #3c4043;
    --google-gray-900: #202124;
  }

  body {
    color: #3c4043;
    background-color: white;
    font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
  }

  .dark body {
    color: #e8eaed;
    background-color: #202124;
  }

  /* Google-style headings */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Google Sans', sans-serif;
    font-weight: 500;
    line-height: 1.3;
  }

  /* Google-style buttons */
  .btn-google {
    @apply inline-flex items-center justify-center px-6 py-2 text-sm font-medium rounded-full transition-all duration-200;
    font-family: 'Google Sans', sans-serif;
  }

  .btn-google-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 hover:shadow-lg;
  }

  .btn-google-secondary {
    @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:shadow-md;
  }

  /* Google-style cards */
  .card-google {
    @apply bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-shadow duration-300;
  }

  .dark .card-google {
    @apply bg-gray-800 border-gray-700;
  }

  /* Google-style search */
  .search-google {
    @apply w-full px-4 py-3 text-base border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
    font-family: 'Roboto', sans-serif;
  }
}

/* Enhanced Code highlighting styles */
.line--highlighted {
  @apply bg-yellow-100 border-l-4 border-yellow-400 pl-4 dark:bg-yellow-900/20;
}

.word--highlighted {
  @apply bg-yellow-200 px-1 py-0.5 rounded dark:bg-yellow-800;
}

/* Anchor links */
.anchor {
  @apply opacity-0 transition-opacity text-blue-500 hover:text-blue-700;
}

h1:hover .anchor,
h2:hover .anchor,
h3:hover .anchor,
h4:hover .anchor,
h5:hover .anchor,
h6:hover .anchor {
  @apply opacity-100;
}

/* Enhanced Prose styles for MDX content */
.prose {
  @apply max-w-none text-gray-800 dark:text-gray-200;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  @apply text-gray-900 dark:text-gray-100;
  font-family: 'Google Sans', sans-serif;
}

.prose h1 {
  @apply text-3xl font-semibold mb-6 mt-8;
}

.prose h2 {
  @apply text-2xl font-semibold mb-4 mt-6 border-b border-gray-200 pb-2 dark:border-gray-700;
}

.prose h3 {
  @apply text-xl font-medium mb-3 mt-5;
}

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul, .prose ol {
  @apply mb-4 pl-6;
}

.prose li {
  @apply mb-2;
}

.prose blockquote {
  @apply border-l-4 border-blue-500 pl-4 italic bg-blue-50 py-2 my-4 dark:bg-blue-900/20;
}

.prose pre {
  @apply bg-gray-100 border border-gray-200 rounded-lg p-4 overflow-x-auto my-4 dark:bg-gray-800 dark:border-gray-700;
}

.prose code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono dark:bg-gray-800;
}

.prose pre code {
  @apply bg-transparent p-0 text-sm;
}

.prose a {
  @apply text-blue-600 hover:text-blue-800 underline dark:text-blue-400 dark:hover:text-blue-300;
}

.prose img {
  @apply rounded-lg shadow-md my-4;
}

.prose table {
  @apply w-full border-collapse border border-gray-300 my-4 dark:border-gray-600;
}

.prose th, .prose td {
  @apply border border-gray-300 px-4 py-2 dark:border-gray-600;
}

.prose th {
  @apply bg-gray-100 font-semibold dark:bg-gray-700;
}

/* Syntax highlighting themes */
.prose .hljs {
  @apply bg-gray-100 dark:bg-gray-800;
}

.prose .hljs-comment {
  @apply text-gray-500 italic;
}

.prose .hljs-keyword {
  @apply text-purple-600 font-semibold dark:text-purple-400;
}

.prose .hljs-string {
  @apply text-green-600 dark:text-green-400;
}

.prose .hljs-number {
  @apply text-blue-600 dark:text-blue-400;
}

.prose .hljs-function {
  @apply text-red-600 dark:text-red-400;
}
