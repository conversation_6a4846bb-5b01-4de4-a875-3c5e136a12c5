import unittest
import sys
import os
import io
from unittest.mock import patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# 导入要测试的模块
import extract_filename

class TestExtractFilename(unittest.TestCase):
    """测试extract_filename.py的功能"""
    
    def test_file_extraction(self):
        """测试文件名提取功能"""
        # 使用patch来捕获print输出
        with patch('sys.stdout', new=io.StringIO()) as fake_stdout:
            # 重新运行模块中的代码
            # 由于模块中的代码在导入时就执行了，我们需要重新执行一次
            # 这里我们直接调用模块中定义的变量和函数
            
            # 预期的输出文件名
            expected_files = ["0.docx", "1.docx", "2.docx"]
            
            # 检查filenames变量是否包含预期的文件名
            self.assertEqual(set(extract_filename.filenames), set(expected_files))
            
            # 检查打印输出
            output = fake_stdout.getvalue().strip().split('\n')
            self.assertEqual(set(output), set(expected_files))
    
    def test_json_parsing(self):
        """测试JSON解析功能"""
        # 测试数据解析是否正确
        self.assertIsInstance(extract_filename.data, dict)
        self.assertIn("json", extract_filename.data)
        self.assertIsInstance(extract_filename.data["json"], list)
        
        # 检查items是否被正确解析
        items = extract_filename.data["json"][0]["items"]
        self.assertIsInstance(items, list)
        
        # 检查文件计数
        file_count = sum(1 for item in items if item.get("type") == "file")
        self.assertEqual(file_count, 3)

if __name__ == '__main__':
    unittest.main()
