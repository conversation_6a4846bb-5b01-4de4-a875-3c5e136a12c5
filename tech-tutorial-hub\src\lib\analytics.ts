// Google Analytics 4 配置
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID || ''

// 检查是否在生产环境且有有效的跟踪ID
export const isProduction = process.env.NODE_ENV === 'production'
export const isAnalyticsEnabled = isProduction && GA_TRACKING_ID

// 页面浏览事件
export const pageview = (url: string) => {
  if (!isAnalyticsEnabled) return
  
  window.gtag('config', GA_TRACKING_ID, {
    page_location: url,
  })
}

// 自定义事件跟踪
interface EventParams {
  action: string
  category: string
  label?: string
  value?: number
  custom_parameters?: Record<string, any>
}

export const event = ({ action, category, label, value, custom_parameters }: EventParams) => {
  if (!isAnalyticsEnabled) return

  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
    ...custom_parameters,
  })
}

// 预定义的事件跟踪函数
export const trackEvent = {
  // 文章阅读事件
  articleView: (articleTitle: string, category: string) => {
    event({
      action: 'view_article',
      category: 'engagement',
      label: articleTitle,
      custom_parameters: {
        article_category: category,
        content_type: 'article'
      }
    })
  },

  // 文章阅读完成
  articleComplete: (articleTitle: string, readingTime: number) => {
    event({
      action: 'article_complete',
      category: 'engagement',
      label: articleTitle,
      value: readingTime,
      custom_parameters: {
        reading_time: readingTime
      }
    })
  },

  // 搜索事件
  search: (searchTerm: string, resultsCount: number) => {
    event({
      action: 'search',
      category: 'engagement',
      label: searchTerm,
      value: resultsCount,
      custom_parameters: {
        search_term: searchTerm,
        results_count: resultsCount
      }
    })
  },

  // 导航事件
  navigation: (from: string, to: string) => {
    event({
      action: 'navigate',
      category: 'navigation',
      label: `${from} -> ${to}`,
      custom_parameters: {
        from_page: from,
        to_page: to
      }
    })
  },

  // 外部链接点击
  externalLink: (url: string, linkText: string) => {
    event({
      action: 'click_external_link',
      category: 'engagement',
      label: url,
      custom_parameters: {
        link_url: url,
        link_text: linkText
      }
    })
  },

  // 代码复制事件
  codeCopy: (language: string, codeSnippet: string) => {
    event({
      action: 'copy_code',
      category: 'engagement',
      label: language,
      custom_parameters: {
        code_language: language,
        code_length: codeSnippet.length
      }
    })
  },

  // 分享事件
  share: (platform: string, articleTitle: string) => {
    event({
      action: 'share',
      category: 'engagement',
      label: platform,
      custom_parameters: {
        share_platform: platform,
        article_title: articleTitle
      }
    })
  },

  // 错误事件
  error: (errorType: string, errorMessage: string, page: string) => {
    event({
      action: 'error',
      category: 'error',
      label: errorType,
      custom_parameters: {
        error_message: errorMessage,
        error_page: page
      }
    })
  },

  // 性能事件
  performance: (metric: string, value: number, page: string) => {
    event({
      action: 'performance_metric',
      category: 'performance',
      label: metric,
      value: Math.round(value),
      custom_parameters: {
        metric_name: metric,
        page_path: page
      }
    })
  },

  // 用户参与度
  engagement: (action: string, element: string, duration?: number) => {
    event({
      action: `engagement_${action}`,
      category: 'user_engagement',
      label: element,
      value: duration,
      custom_parameters: {
        engagement_type: action,
        element_type: element,
        duration_seconds: duration
      }
    })
  }
}

// 用户属性设置
export const setUserProperties = (properties: Record<string, any>) => {
  if (!isAnalyticsEnabled) return

  window.gtag('config', GA_TRACKING_ID, {
    custom_map: properties
  })
}

// 电子商务事件（为未来AdSense收入跟踪准备）
export const ecommerce = {
  // 广告点击
  adClick: (adUnit: string, adPosition: string, revenue?: number) => {
    event({
      action: 'ad_click',
      category: 'monetization',
      label: adUnit,
      value: revenue ? Math.round(revenue * 100) : undefined,
      custom_parameters: {
        ad_unit: adUnit,
        ad_position: adPosition,
        revenue_cents: revenue ? Math.round(revenue * 100) : 0
      }
    })
  },

  // 广告展示
  adImpression: (adUnit: string, adPosition: string) => {
    event({
      action: 'ad_impression',
      category: 'monetization',
      label: adUnit,
      custom_parameters: {
        ad_unit: adUnit,
        ad_position: adPosition
      }
    })
  }
}

// 声明全局gtag函数
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js' | 'set',
      targetId: string | Date,
      config?: Record<string, any>
    ) => void
  }
}
