# Install missing dependencies
Write-Host "Installing missing dependencies..." -ForegroundColor Yellow

try {
    Write-Host "Installing clsx and tailwind-merge..." -ForegroundColor Gray
    npm install clsx tailwind-merge --save
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Dependencies installed successfully!" -ForegroundColor Green
    } else {
        Write-Host "✗ Installation failed with exit code $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Installation failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Checking installation..." -ForegroundColor Yellow
if (Test-Path "node_modules\clsx") {
    Write-Host "✓ clsx installed" -ForegroundColor Green
} else {
    Write-Host "✗ clsx not found" -ForegroundColor Red
}

if (Test-Path "node_modules\tailwind-merge") {
    Write-Host "✓ tailwind-merge installed" -ForegroundColor Green
} else {
    Write-Host "✗ tailwind-merge not found" -ForegroundColor Red
}

Write-Host "Installation complete!" -ForegroundColor Cyan
