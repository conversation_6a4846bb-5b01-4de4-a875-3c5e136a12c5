'use client'

import Script from 'next/script'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect } from 'react'
import { GA_TRACKING_ID, isAnalyticsEnabled, pageview } from '@/lib/analytics'

export function GoogleAnalytics() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!isAnalyticsEnabled) return

    const url = pathname + searchParams.toString()
    pageview(url)
  }, [pathname, searchParams])

  if (!isAnalyticsEnabled) {
    return null
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_TRACKING_ID}', {
              page_location: window.location.href,
              page_title: document.title,
              send_page_view: false
            });
          `,
        }}
      />
    </>
  )
}

// 用于跟踪用户交互的Hook
export function useAnalytics() {
  return {
    trackEvent: (eventName: string, parameters?: Record<string, any>) => {
      if (!isAnalyticsEnabled) return

      window.gtag('event', eventName, {
        ...parameters,
        timestamp: new Date().toISOString()
      })
    },
    
    trackPageView: (url: string, title?: string) => {
      if (!isAnalyticsEnabled) return

      window.gtag('config', GA_TRACKING_ID, {
        page_location: url,
        page_title: title || document.title
      })
    },
    
    setUserProperty: (property: string, value: any) => {
      if (!isAnalyticsEnabled) return

      window.gtag('set', {
        [property]: value
      })
    }
  }
}
