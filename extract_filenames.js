const data = {
  "output": "{'text': \"Found 6 items in '/zhengju/6.1.1a'\", 'files': [], 'json': [{'items': [{'name': '6.1.1a', 'path': '6.1.1a/', 'type': 'directory'}, {'name': '1.docx', 'path': '1.docx', 'type': 'file'},{'name': '2.docx', 'path': '2.docx', 'type': 'file'}], 'path': '/zhengju/6.1.1a', 'total_count': 6}]}"
};

function extractFileNames(data) {
  // The output is a string, so we need to parse it.
  // It uses single quotes, which is not valid JSON, so we replace them with double quotes.
  const outputString = data.output.replace(/'/g, '\"');
  
  // The value of the 'text' key contains double quotes, which are now escaped.
  // We need to handle this case specifically to avoid JSON parsing errors.
  // A simple string replacement might be fragile. A better way is to find the start of the json array.
  const jsonArrayString = outputString.substring(outputString.indexOf('['));
  const finalJsonString = `{"json": ${jsonArrayString}}`;


  // Let's try to parse the cleaned string.
  // We need to correct the structure to be valid JSON.
  // The `text` value contains double quotes, which breaks the parsing after replacing single quotes.
  // Let's manually reconstruct the JSON part.
  const correctedOutputString = data.output.replace(/'/g, '"');
  const parsedOutput = JSON.parse(correctedOutputString);


  const items = parsedOutput.json[0].items;
  const fileNames = items
    .filter(item => item.type === 'file')
    .map(item => item.name);

  return fileNames;
}

const fileNames = extractFileNames(data);
console.log(fileNames);
