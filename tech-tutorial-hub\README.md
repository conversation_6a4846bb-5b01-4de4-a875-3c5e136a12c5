# Tech Tutorial Hub

一个现代化的技术教程网站，专注于提供高质量的编程教程和技术指南。基于 Next.js 14 构建，具有完整的 SEO 优化、性能监控和 AdSense 集成准备。

## ✨ 特性

- 🚀 **现代技术栈**：Next.js 14 + TypeScript + Tailwind CSS
- 📝 **内容管理**：基于 Markdown 的文件系统 CMS
- 🔍 **SEO 优化**：完整的 meta 标签、结构化数据、sitemap
- 📊 **分析监控**：Google Analytics 4 + 性能监控
- 🎨 **响应式设计**：移动端友好，支持深色模式
- 🔎 **搜索功能**：全文搜索、分类筛选、标签系统
- 💰 **AdSense 就绪**：预留广告位，符合 AdSense 政策

## 🚀 快速开始

### 环境要求

- Node.js 18.0.0 或更高版本
- npm 8.0.0 或更高版本

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd tech-tutorial-hub

# 安装依赖
npm install

# 环境检查
npm run setup

# 启动开发服务器
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

### 环境变量配置

复制 `.env.example` 到 `.env.local` 并配置：

```bash
# Google Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# 网站配置
NEXT_PUBLIC_SITE_URL=https://your-domain.com

# 搜索引擎验证
GOOGLE_SITE_VERIFICATION=your-verification-code
```

## 📁 项目结构

```
tech-tutorial-hub/
├── content/posts/          # Markdown 文章
├── src/
│   ├── app/                # Next.js App Router 页面
│   ├── components/         # React 组件
│   ├── lib/                # 工具函数和配置
│   └── styles/             # 样式文件
├── public/                 # 静态资源
├── scripts/                # 构建和部署脚本
└── docs/                   # 文档
```

## 📝 内容管理

### 创建新文章

```bash
# 创建文章模板
npm run content create "文章标题" "分类" "难度"

# 分析内容质量
npm run content:analyze

# 查看内容状态
npm run content:status
```

### 文章格式

```markdown
---
title: "文章标题"
description: "文章描述"
date: "2024-01-01"
author: "作者名"
category: "分类"
tags: ["标签1", "标签2"]
featured: false
draft: false
difficulty: "intermediate"
readTime: "15分钟"
---

# 文章内容

这里是文章正文...
```

## 🔧 开发命令

```bash
# 开发
npm run dev              # 启动开发服务器
npm run build            # 构建生产版本
npm run start            # 启动生产服务器
npm run lint             # 代码检查

# 内容管理
npm run content          # 内容工作流工具
npm run content:analyze  # 分析内容质量
npm run content:status   # 查看内容状态
npm run content:ideas    # 生成内容创意

# 部署
npm run deploy:check     # 部署前检查
npm run deploy:build     # 检查并构建
npm run seo:check        # SEO 检查
```

## 🚀 部署

### 部署前检查

```bash
npm run deploy:check
```

### Vercel 部署（推荐）

1. 将代码推送到 GitHub
2. 在 [Vercel](https://vercel.com) 导入项目
3. 配置环境变量
4. 部署

### 其他平台

项目也可以部署到 Netlify、Railway 等平台。

## 💰 AdSense 申请

部署完成后，参考 `docs/adsense-checklist.md` 进行 AdSense 申请：

1. ✅ 确保网站运行稳定
2. ✅ 内容数量充足（20+ 篇文章）
3. ✅ 完善隐私政策和服务条款
4. ✅ 配置 Google Analytics 和 Search Console
5. 📋 提交 AdSense 申请

## 📊 SEO 优化

- ✅ 完整的 meta 标签和 Open Graph
- ✅ 结构化数据（JSON-LD）
- ✅ XML 网站地图自动生成
- ✅ robots.txt 配置
- ✅ 面包屑导航
- ✅ 图片优化和懒加载

## 🔍 分析和监控

- ✅ Google Analytics 4 集成
- ✅ 性能监控和 Web Vitals
- ✅ 用户行为跟踪
- ✅ 错误监控
- ✅ 搜索控制台集成

## 🛠️ 技术栈

- **框架**：Next.js 14 (App Router)
- **语言**：TypeScript
- **样式**：Tailwind CSS
- **内容**：Markdown + MDX
- **分析**：Google Analytics 4
- **部署**：Vercel (推荐)

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系

- 邮箱：<EMAIL>
- 网站：https://techtutorialhub.com
