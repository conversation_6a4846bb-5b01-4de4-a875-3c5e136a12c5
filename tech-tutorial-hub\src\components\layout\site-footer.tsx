import Link from "next/link"
import { siteConfig } from "@/lib/constants"

export function SiteFooter() {
  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* 网站信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{siteConfig.name}</h3>
            <p className="text-sm text-muted-foreground">
              专业的技术教程平台，提供高质量的编程指南、开发技巧和最佳实践。
            </p>
          </div>

          {/* 快速链接 */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">快速链接</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/tutorials" className="text-muted-foreground hover:text-foreground">
                  教程
                </Link>
              </li>
              <li>
                <Link href="/categories" className="text-muted-foreground hover:text-foreground">
                  分类
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-muted-foreground hover:text-foreground">
                  关于我们
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-muted-foreground hover:text-foreground">
                  联系我们
                </Link>
              </li>
            </ul>
          </div>

          {/* 技术分类 */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">技术分类</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/categories/react" className="text-muted-foreground hover:text-foreground">
                  React
                </Link>
              </li>
              <li>
                <Link href="/categories/nextjs" className="text-muted-foreground hover:text-foreground">
                  Next.js
                </Link>
              </li>
              <li>
                <Link href="/categories/typescript" className="text-muted-foreground hover:text-foreground">
                  TypeScript
                </Link>
              </li>
              <li>
                <Link href="/categories/css" className="text-muted-foreground hover:text-foreground">
                  CSS
                </Link>
              </li>
            </ul>
          </div>

          {/* 法律信息 */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">法律信息</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/privacy" className="text-muted-foreground hover:text-foreground">
                  隐私政策
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-muted-foreground hover:text-foreground">
                  使用条款
                </Link>
              </li>
              <li>
                <Link href="/sitemap.xml" className="text-muted-foreground hover:text-foreground">
                  网站地图
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* 版权信息 */}
        <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
          <p>
            © {new Date().getFullYear()} {siteConfig.name}. 保留所有权利。
          </p>
          <p className="mt-2">
            使用 Next.js 和 Tailwind CSS 构建 | 
            <Link href="https://github.com" className="ml-1 hover:text-foreground">
              查看源码
            </Link>
          </p>
        </div>
      </div>
    </footer>
  )
}
