"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { getAllTags } from "@/lib/content-manager"
import { cn } from "@/lib/utils"

interface ContentFiltersProps {
  className?: string
}

export function ContentFilters({ className = "" }: ContentFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>("")
  const [sortBy, setSortBy] = useState<string>("date")
  
  const tags = getAllTags()
  const difficulties = [
    { value: "beginner", label: "初级", icon: "🟢" },
    { value: "intermediate", label: "中级", icon: "🟡" },
    { value: "advanced", label: "高级", icon: "🔴" }
  ]
  
  const sortOptions = [
    { value: "date", label: "最新发布" },
    { value: "title", label: "标题排序" },
    { value: "readTime", label: "阅读时间" },
    { value: "category", label: "分类排序" }
  ]

  // Initialize from URL params
  useEffect(() => {
    const tags = searchParams.get("tags")?.split(",").filter(Boolean) || []
    const difficulty = searchParams.get("difficulty") || ""
    const sort = searchParams.get("sort") || "date"
    
    setSelectedTags(tags)
    setSelectedDifficulty(difficulty)
    setSortBy(sort)
  }, [searchParams])

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString())
    
    // Update tags
    if (selectedTags.length > 0) {
      params.set("tags", selectedTags.join(","))
    } else {
      params.delete("tags")
    }
    
    // Update difficulty
    if (selectedDifficulty) {
      params.set("difficulty", selectedDifficulty)
    } else {
      params.delete("difficulty")
    }
    
    // Update sort
    if (sortBy !== "date") {
      params.set("sort", sortBy)
    } else {
      params.delete("sort")
    }
    
    const newUrl = params.toString() ? `?${params.toString()}` : ""
    router.push(`/tutorials${newUrl}`, { scroll: false })
  }, [selectedTags, selectedDifficulty, sortBy, router, searchParams])

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  const clearAllFilters = () => {
    setSelectedTags([])
    setSelectedDifficulty("")
    setSortBy("date")
  }

  const hasActiveFilters = selectedTags.length > 0 || selectedDifficulty || sortBy !== "date"

  return (
    <div className={cn("space-y-6", className)}>
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          筛选条件
        </h3>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            清除全部
          </button>
        )}
      </div>

      {/* Sort Options */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          排序方式
        </h4>
        <div className="space-y-2">
          {sortOptions.map((option) => (
            <label key={option.value} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="sort"
                value={option.value}
                checked={sortBy === option.value}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {option.label}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Difficulty Filter */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          难度等级
        </h4>
        <div className="space-y-2">
          {difficulties.map((difficulty) => (
            <label key={difficulty.value} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="difficulty"
                value={difficulty.value}
                checked={selectedDifficulty === difficulty.value}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              />
              <span className="text-lg">{difficulty.icon}</span>
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {difficulty.label}
              </span>
            </label>
          ))}
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="radio"
              name="difficulty"
              value=""
              checked={selectedDifficulty === ""}
              onChange={() => setSelectedDifficulty("")}
              className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">
              全部难度
            </span>
          </label>
        </div>
      </div>

      {/* Tags Filter */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          标签筛选
        </h4>
        <div className="flex flex-wrap gap-2">
          {tags.slice(0, 12).map((tag) => (
            <button
              key={tag.name}
              onClick={() => toggleTag(tag.name)}
              className={cn(
                "px-3 py-1 rounded-full text-sm font-medium transition-colors",
                selectedTags.includes(tag.name)
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
              )}
            >
              {tag.name}
              <span className="ml-1 text-xs opacity-75">
                {tag.count}
              </span>
            </button>
          ))}
        </div>
        {tags.length > 12 && (
          <button className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
            显示更多标签...
          </button>
        )}
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="space-y-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            当前筛选
          </h4>
          <div className="space-y-2">
            {selectedDifficulty && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">难度:</span>
                <span className="font-medium">
                  {difficulties.find(d => d.value === selectedDifficulty)?.label}
                </span>
              </div>
            )}
            {selectedTags.length > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">标签:</span>
                <span className="font-medium">
                  {selectedTags.length} 个已选择
                </span>
              </div>
            )}
            {sortBy !== "date" && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">排序:</span>
                <span className="font-medium">
                  {sortOptions.find(s => s.value === sortBy)?.label}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// Compact filter bar for mobile
export function ContentFiltersCompact({ className = "" }: { className?: string }) {
  const searchParams = useSearchParams()
  const selectedTags = searchParams.get("tags")?.split(",").filter(Boolean) || []
  const selectedDifficulty = searchParams.get("difficulty") || ""
  const sortBy = searchParams.get("sort") || "date"
  
  const hasActiveFilters = selectedTags.length > 0 || selectedDifficulty || sortBy !== "date"
  
  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <span className="text-sm text-gray-600 dark:text-gray-400">筛选:</span>
      
      {!hasActiveFilters && (
        <span className="text-sm text-gray-500">无</span>
      )}
      
      {selectedDifficulty && (
        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full dark:bg-blue-900 dark:text-blue-200">
          {selectedDifficulty}
        </span>
      )}
      
      {selectedTags.slice(0, 3).map(tag => (
        <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full dark:bg-gray-800 dark:text-gray-300">
          {tag}
        </span>
      ))}
      
      {selectedTags.length > 3 && (
        <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full dark:bg-gray-800 dark:text-gray-300">
          +{selectedTags.length - 3}
        </span>
      )}
    </div>
  )
}
