import { Metadata } from 'next'
import { generateMetadata } from '@/lib/seo'

export const metadata: Metadata = generateMetadata({
  title: '关于我们',
  description: '了解 Tech Tutorial Hub - 专业的技术教程平台，致力于提供高质量的编程指南和开发资源',
  url: '/about'
})

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8">关于我们</h1>
      
      <div className="prose prose-lg max-w-none dark:prose-invert">
        <section className="mb-12">
          <h2>我们的使命</h2>
          <p>
            Tech Tutorial Hub 致力于为开发者和技术爱好者提供高质量、实用的技术教程和编程指南。
            我们相信知识应该是开放和易于获取的，每个人都应该有机会学习和掌握最新的技术。
          </p>
        </section>

        <section className="mb-12">
          <h2>我们的愿景</h2>
          <p>
            成为全球领先的中文技术教育平台，帮助数百万开发者提升技能，推动技术社区的发展和创新。
          </p>
        </section>

        <section className="mb-12">
          <h2>我们提供什么</h2>
          <div className="grid md:grid-cols-2 gap-8 my-8">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-blue-900 dark:text-blue-100 mb-3">
                📚 高质量教程
              </h3>
              <p className="text-blue-800 dark:text-blue-200">
                深入浅出的技术教程，从基础概念到高级应用，帮助您系统性地学习技术知识。
              </p>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-green-900 dark:text-green-100 mb-3">
                💻 实践项目
              </h3>
              <p className="text-green-800 dark:text-green-200">
                真实的项目案例和代码示例，让您在实践中掌握技术，提升解决问题的能力。
              </p>
            </div>
            
            <div className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-purple-900 dark:text-purple-100 mb-3">
                🚀 最新技术
              </h3>
              <p className="text-purple-800 dark:text-purple-200">
                紧跟技术发展趋势，及时更新内容，确保您学到的都是最新、最实用的技术。
              </p>
            </div>
            
            <div className="bg-orange-50 dark:bg-orange-900/20 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-orange-900 dark:text-orange-100 mb-3">
                🎯 学习路径
              </h3>
              <p className="text-orange-800 dark:text-orange-200">
                结构化的学习路径和难度分级，无论您是初学者还是资深开发者，都能找到适合的内容。
              </p>
            </div>
          </div>
        </section>

        <section className="mb-12">
          <h2>技术覆盖</h2>
          <p>我们的教程涵盖广泛的技术领域：</p>
          <ul>
            <li><strong>前端开发</strong>：React、Vue、JavaScript、TypeScript、CSS、HTML</li>
            <li><strong>后端开发</strong>：Node.js、Python、Java、Go、数据库设计</li>
            <li><strong>全栈框架</strong>：Next.js、Nuxt.js、Django、Express</li>
            <li><strong>移动开发</strong>：React Native、Flutter、原生开发</li>
            <li><strong>云计算</strong>：AWS、Azure、Docker、Kubernetes</li>
            <li><strong>数据科学</strong>：Python数据分析、机器学习、AI应用</li>
            <li><strong>DevOps</strong>：CI/CD、自动化部署、监控</li>
          </ul>
        </section>

        <section className="mb-12">
          <h2>我们的团队</h2>
          <p>
            我们的团队由经验丰富的软件工程师、技术作家和教育专家组成。每位成员都在各自的技术领域有着深厚的实践经验，
            致力于将复杂的技术概念转化为易于理解的教程内容。
          </p>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mt-6">
            <h3 className="text-lg font-semibold mb-3">我们的原则</h3>
            <ul className="space-y-2">
              <li>✅ <strong>质量第一</strong>：每篇教程都经过严格的技术审核和测试</li>
              <li>✅ <strong>实用导向</strong>：注重实际应用，提供可运行的代码示例</li>
              <li>✅ <strong>持续更新</strong>：定期更新内容，确保技术信息的时效性</li>
              <li>✅ <strong>社区驱动</strong>：倾听用户反馈，不断改进和优化内容</li>
            </ul>
          </div>
        </section>

        <section className="mb-12">
          <h2>加入我们</h2>
          <p>
            如果您是技术专家，热爱分享知识，欢迎加入我们的作者团队。
            如果您是学习者，欢迎关注我们的最新内容，与我们一起成长。
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 mt-6">
            <a 
              href="/contact" 
              className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              联系我们
            </a>
            <a 
              href="/tutorials" 
              className="inline-flex items-center justify-center px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
            >
              开始学习
            </a>
          </div>
        </section>

        <section className="mb-12">
          <h2>联系信息</h2>
          <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">商务合作</h3>
                <p className="text-blue-800 dark:text-blue-200"><EMAIL></p>
              </div>
              <div>
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">技术支持</h3>
                <p className="text-blue-800 dark:text-blue-200"><EMAIL></p>
              </div>
              <div>
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">内容投稿</h3>
                <p className="text-blue-800 dark:text-blue-200"><EMAIL></p>
              </div>
              <div>
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">媒体咨询</h3>
                <p className="text-blue-800 dark:text-blue-200"><EMAIL></p>
              </div>
            </div>
          </div>
        </section>

        <section>
          <h2>致谢</h2>
          <p>
            感谢所有为 Tech Tutorial Hub 贡献内容的作者、提供反馈的用户，以及支持我们的技术社区。
            正是因为有了大家的支持，我们才能不断成长和进步。
          </p>
        </section>
      </div>
    </div>
  )
}
