# Tech Tutorial Hub - Windows PowerShell Setup Script

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Tech Tutorial Hub - Windows Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# 1. Check Node.js installation
Write-Host "`n1. Checking Node.js installation..." -ForegroundColor Yellow
if (Test-Command "node") {
    $nodeVersion = node --version
    Write-Host "✓ Node.js version: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "✗ Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# 2. Check npm installation
Write-Host "`n2. Checking npm installation..." -ForegroundColor Yellow
if (Test-Command "npm") {
    $npmVersion = npm --version
    Write-Host "✓ npm version: $npmVersion" -ForegroundColor Green
} else {
    Write-Host "✗ npm is not available" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# 3. Clean previous installation
Write-Host "`n3. Cleaning previous installation..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "Removing existing node_modules..." -ForegroundColor Gray
    Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
}
if (Test-Path "package-lock.json") {
    Write-Host "Removing package-lock.json..." -ForegroundColor Gray
    Remove-Item "package-lock.json" -ErrorAction SilentlyContinue
}

# 4. Install dependencies
Write-Host "`n4. Installing dependencies..." -ForegroundColor Yellow
Write-Host "This may take a few minutes..." -ForegroundColor Gray

try {
    Write-Host "Trying npm install..." -ForegroundColor Gray
    npm install --verbose
    if ($LASTEXITCODE -ne 0) { throw "npm install failed" }
} catch {
    Write-Host "npm install failed, trying with --legacy-peer-deps..." -ForegroundColor Yellow
    try {
        npm install --legacy-peer-deps --verbose
        if ($LASTEXITCODE -ne 0) { throw "npm install with legacy-peer-deps failed" }
    } catch {
        Write-Host "npm still failing, trying yarn..." -ForegroundColor Yellow
        try {
            npm install -g yarn
            yarn install
            if ($LASTEXITCODE -ne 0) { throw "yarn install failed" }
        } catch {
            Write-Host "All installation methods failed!" -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit 1
        }
    }
}

# 5. Verify installation
Write-Host "`n5. Verifying installation..." -ForegroundColor Yellow
if (Test-Path "node_modules\.bin\next.cmd") {
    Write-Host "✓ Next.js installed successfully" -ForegroundColor Green
} else {
    Write-Host "✗ Next.js installation failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# 6. Start development server
Write-Host "`n6. Starting development server..." -ForegroundColor Yellow
Write-Host "Opening http://localhost:3000 in your browser..." -ForegroundColor Gray

# Open browser
Start-Process "http://localhost:3000"

# Start dev server
Write-Host "`nStarting Next.js development server..." -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Gray
npm run dev
