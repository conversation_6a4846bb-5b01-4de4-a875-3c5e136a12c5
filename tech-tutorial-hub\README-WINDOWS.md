# Tech Tutorial Hub - Windows 安装指南

## 🖥️ Windows 环境要求

- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本
- **操作系统**: Windows 10/11
- **PowerShell**: 5.1 或更高版本

## 🚀 快速开始

### 方法一：使用批处理文件（推荐）

1. 双击运行 `setup-windows.bat`
2. 等待自动安装完成
3. 浏览器会自动打开 http://localhost:3000

### 方法二：使用 PowerShell

1. 以管理员身份运行 PowerShell
2. 执行以下命令：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   .\setup-windows.ps1
   ```

### 方法三：手动安装

1. 打开命令提示符或 PowerShell
2. 导航到项目目录：
   ```cmd
   cd C:\Users\<USER>\Documents\augment-projects\googlead\tech-tutorial-hub
   ```
3. 安装依赖：
   ```cmd
   npm install
   ```
4. 启动开发服务器：
   ```cmd
   npm run dev
   ```

## 🔧 常见问题解决

### 问题 1: npm install 失败

**解决方案 A - 使用 legacy-peer-deps:**
```cmd
npm install --legacy-peer-deps
```

**解决方案 B - 清理缓存:**
```cmd
npm cache clean --force
rmdir /s /q node_modules
del package-lock.json
npm install
```

**解决方案 C - 使用 yarn:**
```cmd
npm install -g yarn
yarn install
yarn dev
```

### 问题 2: 权限错误

1. 以管理员身份运行命令提示符
2. 或者设置 npm 权限：
   ```cmd
   npm config set cache C:\npm-cache --global
   ```

### 问题 3: 路径过长错误

1. 启用长路径支持：
   ```cmd
   git config --system core.longpaths true
   ```
2. 或者将项目移动到更短的路径，如 `C:\projects\tech-hub`

### 问题 4: 端口被占用

如果 3000 端口被占用，可以指定其他端口：
```cmd
npm run dev -- -p 3001
```

## 🛠️ Windows 专用命令

项目包含了 Windows 优化的命令：

- `npm run dev:windows` - 使用更大内存限制启动开发服务器
- `npm run build:windows` - 使用更大内存限制构建项目
- `npm run setup` - 运行环境检查

## 📁 项目结构

```
tech-tutorial-hub/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── layout.tsx       # 主布局
│   │   ├── page.tsx         # 首页
│   │   ├── posts/           # 文章页面
│   │   └── tutorials/       # 教程列表
│   ├── components/          # React 组件
│   │   ├── ui/             # 基础 UI 组件
│   │   └── layout/         # 布局组件
│   ├── lib/                # 工具函数
│   └── types/              # TypeScript 类型
├── content/                # MDX 内容文件
├── public/                 # 静态资源
├── setup-windows.bat       # Windows 批处理安装脚本
├── setup-windows.ps1       # PowerShell 安装脚本
└── setup-check.js          # 环境检查脚本
```

## 🎯 开发工作流

1. **启动开发服务器:**
   ```cmd
   npm run dev
   ```

2. **检查代码质量:**
   ```cmd
   npm run lint
   ```

3. **构建生产版本:**
   ```cmd
   npm run build
   ```

4. **启动生产服务器:**
   ```cmd
   npm run start
   ```

## 🔍 环境检查

运行环境检查脚本来验证设置：
```cmd
npm run setup
```

这会检查：
- Node.js 和 npm 版本
- 项目文件完整性
- 依赖安装状态
- Windows 特定配置

## 💡 Windows 开发技巧

1. **使用 Windows Terminal** 获得更好的命令行体验
2. **安装 Git for Windows** 获得更好的 Git 支持
3. **使用 VS Code** 作为推荐的代码编辑器
4. **启用开发者模式** 以避免权限问题

## 🆘 获取帮助

如果遇到问题：

1. 检查 Node.js 和 npm 版本是否符合要求
2. 尝试清理 npm 缓存和 node_modules
3. 以管理员身份运行命令
4. 查看项目的 GitHub Issues 页面

## 📞 联系支持

- 项目仓库: [GitHub Repository]
- 问题报告: [GitHub Issues]
- 文档: [项目文档]

---

**注意**: 这个项目专门为 Windows 环境进行了优化，包含了解决常见 Windows 开发问题的脚本和配置。
