import { z } from 'zod'

// Content validation schemas
export const PostMetadataSchema = z.object({
  title: z.string().min(1, "Title is required").max(100, "Title too long"),
  description: z.string().min(1, "Description is required").max(200, "Description too long"),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  author: z.string().min(1, "Author is required"),
  category: z.string().min(1, "Category is required"),
  tags: z.array(z.string()).optional(),
  featured: z.boolean().optional(),
  draft: z.boolean().optional(),
  readTime: z.string().optional(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  prerequisites: z.array(z.string()).optional(),
  learningObjectives: z.array(z.string()).optional(),
})

export const PostContentSchema = z.object({
  metadata: PostMetadataSchema,
  content: z.string().min(100, "Content must be at least 100 characters"),
  slug: z.string().regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
})

export type PostMetadata = z.infer<typeof PostMetadataSchema>
export type PostContent = z.infer<typeof PostContentSchema>

// Content validation functions
export function validatePostMetadata(metadata: unknown): PostMetadata {
  return PostMetadataSchema.parse(metadata)
}

export function validatePostContent(content: unknown): PostContent {
  return PostContentSchema.parse(content)
}

// Content quality checks
export function checkContentQuality(content: string): {
  score: number
  issues: string[]
  suggestions: string[]
} {
  const issues: string[] = []
  const suggestions: string[] = []
  let score = 100

  // Check content length
  if (content.length < 500) {
    issues.push("Content is too short (less than 500 characters)")
    score -= 20
  }

  // Check for headings structure
  const headings = content.match(/^#{1,6}\s+.+$/gm) || []
  if (headings.length < 2) {
    issues.push("Content should have at least 2 headings for better structure")
    score -= 10
  }

  // Check for code blocks
  const codeBlocks = content.match(/```[\s\S]*?```/g) || []
  if (codeBlocks.length === 0 && content.includes('code')) {
    suggestions.push("Consider adding code examples with syntax highlighting")
  }

  // Check for images
  const images = content.match(/!\[.*?\]\(.*?\)/g) || []
  if (images.length === 0) {
    suggestions.push("Consider adding relevant images or diagrams")
  }

  // Check for links
  const links = content.match(/\[.*?\]\(.*?\)/g) || []
  if (links.length < 2) {
    suggestions.push("Consider adding more relevant external links")
  }

  // Check for lists
  const lists = content.match(/^[\s]*[-*+]\s+.+$/gm) || []
  if (lists.length === 0) {
    suggestions.push("Consider using bullet points or numbered lists for better readability")
  }

  return { score, issues, suggestions }
}

// SEO content analysis
export function analyzeSEO(title: string, description: string, content: string): {
  score: number
  issues: string[]
  suggestions: string[]
} {
  const issues: string[] = []
  const suggestions: string[] = []
  let score = 100

  // Title analysis
  if (title.length < 30) {
    issues.push("Title is too short for SEO (should be 30-60 characters)")
    score -= 15
  } else if (title.length > 60) {
    issues.push("Title is too long for SEO (should be 30-60 characters)")
    score -= 10
  }

  // Description analysis
  if (description.length < 120) {
    issues.push("Meta description is too short (should be 120-160 characters)")
    score -= 15
  } else if (description.length > 160) {
    issues.push("Meta description is too long (should be 120-160 characters)")
    score -= 10
  }

  // Keyword density check (basic)
  const words = content.toLowerCase().split(/\s+/)
  const wordCount = words.length
  const titleWords = title.toLowerCase().split(/\s+/)
  
  titleWords.forEach(word => {
    if (word.length > 3) {
      const occurrences = words.filter(w => w.includes(word)).length
      const density = (occurrences / wordCount) * 100
      
      if (density < 0.5) {
        suggestions.push(`Consider using the keyword "${word}" more frequently in the content`)
      } else if (density > 3) {
        issues.push(`Keyword "${word}" may be overused (${density.toFixed(1)}% density)`)
        score -= 5
      }
    }
  })

  return { score, issues, suggestions }
}

// Content readability analysis
export function analyzeReadability(content: string): {
  score: number
  level: string
  suggestions: string[]
} {
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
  const words = content.split(/\s+/).filter(w => w.length > 0)
  const syllables = words.reduce((count, word) => {
    return count + countSyllables(word)
  }, 0)

  // Flesch Reading Ease Score
  const avgSentenceLength = words.length / sentences.length
  const avgSyllablesPerWord = syllables / words.length
  
  const fleschScore = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord)
  
  let level = 'Graduate'
  let suggestions: string[] = []

  if (fleschScore >= 90) {
    level = 'Very Easy'
  } else if (fleschScore >= 80) {
    level = 'Easy'
  } else if (fleschScore >= 70) {
    level = 'Fairly Easy'
  } else if (fleschScore >= 60) {
    level = 'Standard'
  } else if (fleschScore >= 50) {
    level = 'Fairly Difficult'
  } else if (fleschScore >= 30) {
    level = 'Difficult'
  }

  if (fleschScore < 60) {
    suggestions.push("Consider using shorter sentences for better readability")
    suggestions.push("Try to use simpler words where possible")
  }

  if (avgSentenceLength > 20) {
    suggestions.push("Average sentence length is high. Consider breaking long sentences.")
  }

  return {
    score: Math.max(0, Math.min(100, fleschScore)),
    level,
    suggestions
  }
}

function countSyllables(word: string): number {
  word = word.toLowerCase()
  if (word.length <= 3) return 1
  
  word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '')
  word = word.replace(/^y/, '')
  
  const matches = word.match(/[aeiouy]{1,2}/g)
  return matches ? matches.length : 1
}
