import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatDate } from "@/lib/utils"

// 临时的静态数据
const posts = [
  {
    slug: "react-getting-started",
    title: "React 入门指南：从零开始学习 React",
    description: "全面的 React 入门教程，涵盖组件、状态管理、事件处理等核心概念，适合初学者快速上手",
    date: "2024-01-15",
    tags: ["React", "JavaScript", "前端开发", "入门教程"],
    category: "react",
    author: "Tech Tutorial Hub",
    readingTime: 8
  },
  {
    slug: "javascript-es6-features",
    title: "ES6+ 新特性详解：现代 JavaScript 开发必知",
    description: "深入了解 ES6 及后续版本的重要特性，包括箭头函数、解构赋值、模块化等，提升你的 JavaScript 开发技能",
    date: "2024-01-10",
    tags: ["JavaScript", "ES6", "前端开发", "语言特性"],
    category: "javascript",
    author: "Tech Tutorial Hub",
    readingTime: 12
  }
]

export const metadata = {
  title: "所有教程",
  description: "浏览我们的技术教程集合，从基础到进阶，帮助你提升编程技能",
}

export default function TutorialsPage() {
  return (
    <div className="container py-6 lg:py-10">
      <div className="flex flex-col items-start gap-4 md:flex-row md:justify-between md:gap-8">
        <div className="flex-1 space-y-4">
          <h1 className="inline-block font-bold text-4xl tracking-tight lg:text-5xl">
            所有教程
          </h1>
          <p className="text-xl text-muted-foreground">
            浏览我们精心编写的技术教程，从基础概念到高级技巧
          </p>
        </div>
      </div>
      
      <hr className="my-8" />
      
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {posts.map((post) => (
          <Card key={post.slug} className="group cursor-pointer transition-all hover:shadow-lg">
            <Link href={`/posts/${post.slug}`}>
              <CardHeader>
                <div className="space-y-2">
                  <CardTitle className="line-clamp-2 group-hover:text-primary transition-colors">
                    {post.title}
                  </CardTitle>
                  <CardDescription className="line-clamp-3">
                    {post.description}
                  </CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-1 mb-3">
                  {post.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {post.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{post.tags.length - 3}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>{formatDate(post.date)}</span>
                  <span>{post.readingTime} 分钟阅读</span>
                </div>
              </CardContent>
            </Link>
          </Card>
        ))}
      </div>
      
      {posts.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">暂无教程内容</p>
        </div>
      )}
    </div>
  )
}
