# 从 collections.abc 导入 Generator，用于类型提示生成器
from collections.abc import Generator
# 从 typing 导入 Any 和 Optional，用于类型提示
from typing import Any, Optional, Dict
# 导入 tempfile，用于创建临时文件
import tempfile
# 导入 os，用于操作系统相关功能，如路径操作和文件删除
import os
# 导入 mimetypes，用于检测文件MIME类型
import mimetypes
# 导入 logging，用于日志记录
import logging
# 导入 time，用于重试机制
import time

# 从 dify_plugin 导入 Tool 基类
from dify_plugin import Tool
# 从 dify_plugin.entities.tool 导入 ToolInvokeMessage，用于创建工具调用消息
from dify_plugin.entities.tool import ToolInvokeMessage


# 配置日志记录器
logger = logging.getLogger(__name__)

class DownloadFileTool(Tool):
    """
    一个从 NextCloud 下载文件的工具。
    它继承自 dify_plugin.Tool 类。

    功能：
    - 从 NextCloud 服务器下载文件
    - 获取文件元数据
    - 支持大文件处理和错误重试
    """

    # 最大重试次数
    MAX_RETRIES = 3
    # 重试延迟（秒）
    RETRY_DELAY = 2
    # 最大文件大小限制（默认100MB）
    MAX_FILE_SIZE = 100 * 1024 * 1024

    def _get_file_mime_type(self, file_path: str) -> str:
        """
        根据文件扩展名获取MIME类型

        Args:
            file_path: 文件路径

        Returns:
            文件的MIME类型，如果无法确定则返回'application/octet-stream'
        """
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'
    
    def _validate_parameters(self, tool_parameters: Dict[str, Any]) -> tuple[str, bool, Optional[str]]:
        """
        验证和处理工具参数

        Args:
            tool_parameters: 工具参数字典

        Returns:
            tuple: (file_path, include_content, error_message)
        """
        file_path = tool_parameters.get("file_path", "").strip()
        include_content = tool_parameters.get("include_content", "false").lower() == "true"

        # 验证文件路径
        if not file_path:
            return "", False, "错误：文件路径不能为空"

        # 标准化文件路径
        if not file_path.startswith("/"):
            file_path = "/" + file_path

        # 验证文件路径格式
        if ".." in file_path or file_path.endswith("/"):
            return "", False, "错误：文件路径格式无效"

        return file_path, include_content, None

    def _create_webdav_client(self) -> tuple[Optional[Any], Optional[str]]:
        """
        创建WebDAV客户端

        Returns:
            tuple: (client, error_message)
        """
        try:
            # 动态导入 webdavclient3 包
            from webdav3.client import Client

            # 从运行时获取 NextCloud 凭据
            webdav_hostname = self.runtime.credentials.get("webdav_hostname")
            username = self.runtime.credentials.get("username")
            app_password = self.runtime.credentials.get("app_password")

            # 检查所有凭据是否都已配置
            if not all([webdav_hostname, username, app_password]):
                return None, "错误：NextCloud 凭据配置不完整，请检查 webdav_hostname、username 和 app_password"

            # 确保主机名格式正确
            if not webdav_hostname.endswith('/'):
                webdav_hostname += '/'
            if not webdav_hostname.endswith('remote.php/webdav/'):
                webdav_hostname += 'remote.php/webdav/'

            # 创建 WebDAV 客户端选项
            webdav_options = {
                'webdav_hostname': webdav_hostname,
                'webdav_login': username,
                'webdav_password': app_password,
                'webdav_timeout': 30  # 添加超时设置
            }

            # 创建 WebDAV 客户端实例
            client = Client(webdav_options)
            return client, None

        except ImportError:
            return None, "错误：缺少必需的依赖库 webdavclient3，请先安装：pip install webdavclient3"
        except Exception as e:
            return None, f"创建WebDAV客户端时发生错误: {str(e)}"

    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage, None, None]:
        """
        工具的主调用方法，用于从 NextCloud 下载文件。

        Args:
            tool_parameters: 一个包含工具所需参数的字典。
                            - "file_path": (必须) 要下载的文件的路径。
                            - "include_content": (可选) 是否在响应中包含文件内容，默认为 "false"。

        Yields:
            ToolInvokeMessage: 工具调用消息（文本、JSON 或 Blob）
        """
        # 验证参数
        file_path, include_content, error = self._validate_parameters(tool_parameters)
        if error:
            yield self.create_text_message(error)
            return
        # 创建WebDAV客户端
        client, error = self._create_webdav_client()
        if error:
            yield self.create_text_message(error)
            return
            
            try:
                # 使用重试机制检查文件是否存在
                file_exists = False
                for attempt in range(self.MAX_RETRIES):
                    try:
                        file_exists = client.check(file_path)
                        break
                    except Exception as e:
                        if attempt < self.MAX_RETRIES - 1:
                            logger.warning(f"检查文件存在性失败，尝试重试 ({attempt+1}/{self.MAX_RETRIES}): {str(e)}")
                            time.sleep(self.RETRY_DELAY)
                        else:
                            raise

                if not file_exists:
                    yield self.create_text_message(f"错误：文件 '{file_path}' 不存在")
                    return

                # 获取文件信息（带重试）
                file_info = None
                for attempt in range(self.MAX_RETRIES):
                    try:
                        file_info = client.info(file_path)
                        break
                    except Exception as e:
                        if attempt < self.MAX_RETRIES - 1:
                            logger.warning(f"获取文件信息失败，尝试重试 ({attempt+1}/{self.MAX_RETRIES}): {str(e)}")
                            time.sleep(self.RETRY_DELAY)
                        else:
                            raise

                if not file_info:
                    yield self.create_text_message(f"错误：无法获取文件 '{file_path}' 的信息")
                    return

                file_name = os.path.basename(file_path)
                file_size = int(file_info.get('size', 0))

                # 检测文件MIME类型
                content_type = self._get_file_mime_type(file_path)

                # 准备响应元数据
                response_data = {
                    "name": file_name,
                    "path": file_path,
                    "size": file_size,
                    "modified": file_info.get('modified', ''),
                    "content_type": content_type
                }

                # 如果不需要文件内容，只返回元数据
                if not include_content:
                    summary = f"获取文件信息成功：'{file_name}' (大小: {self._format_file_size(file_size)})"
                    yield self.create_text_message(summary)
                    yield self.create_json_message(response_data)
                    return

                # 检查文件大小限制
                if file_size > self.MAX_FILE_SIZE:
                    yield self.create_text_message(
                        f"错误：文件过大 ({self._format_file_size(file_size)})，"
                        f"超过限制 ({self._format_file_size(self.MAX_FILE_SIZE)})"
                    )
                    return
                
                # 下载文件内容
                temp_path = None
                try:
                    # 创建临时文件
                    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                        temp_path = temp_file.name

                    # 下载文件到临时路径（带进度和重试）
                    yield self.create_text_message(f"正在下载文件 '{file_name}' ({self._format_file_size(file_size)})...")

                    # 使用重试机制下载文件
                    download_success = False
                    for attempt in range(self.MAX_RETRIES):
                        try:
                            client.download_file(file_path, temp_path)
                            download_success = True
                            break
                        except Exception as e:
                            if attempt < self.MAX_RETRIES - 1:
                                yield self.create_text_message(
                                    f"下载失败，正在重试 ({attempt+1}/{self.MAX_RETRIES}): {str(e)}"
                                )
                                time.sleep(self.RETRY_DELAY)
                            else:
                                raise

                    if not download_success:
                        yield self.create_text_message("错误：多次尝试下载文件失败")
                        return

                    # 验证下载的文件大小
                    actual_size = os.path.getsize(temp_path)
                    if actual_size != file_size:
                        yield self.create_text_message(
                            f"警告：下载文件大小不匹配 (期望: {self._format_file_size(file_size)}, "
                            f"实际: {self._format_file_size(actual_size)})"
                        )

                    # 读取文件内容
                    with open(temp_path, 'rb') as f:
                        file_content = f.read()

                    # 验证读取的内容大小
                    if len(file_content) == 0:
                        yield self.create_text_message("错误：下载的文件内容为空")
                        return

                    # 发送成功消息和文件内容
                    summary = (
                        f"文件下载成功：'{file_name}' "
                        f"(大小: {self._format_file_size(len(file_content))}, 类型: {content_type})"
                    )
                    yield self.create_text_message(summary)

                    # 创建包含文件内容的 blob 消息
                    yield self.create_blob_message(
                        blob=file_content,
                        meta={
                            "file_name": file_name,
                            "file_path": file_path,
                            "content_type": content_type,
                            "size": len(file_content),
                            "modified": file_info.get('modified', '')
                        }
                    )
                    
                except Exception as download_error:
                    yield self.create_text_message(f"下载文件时发生错误: {str(download_error)}")
                    return

                finally:
                    # 清理临时文件
                    if temp_path and os.path.exists(temp_path):
                        try:
                            os.unlink(temp_path)
                        except Exception as cleanup_error:
                            logger.warning(f"清理临时文件失败: {str(cleanup_error)}")

            except Exception as webdav_error:
                # 处理 WebDAV 相关错误
                error_message = self._handle_webdav_error(webdav_error, file_path)
                yield self.create_text_message(error_message)
                return

        except Exception as e:
            # 处理其他意外错误
            logger.error(f"工具执行时发生意外错误: {str(e)}")
            yield self.create_text_message(f"发生意外错误: {str(e)}")
            return

    def _handle_webdav_error(self, error: Exception, file_path: str) -> str:
        """
        处理WebDAV相关错误并返回用户友好的错误消息

        Args:
            error: WebDAV异常
            file_path: 文件路径

        Returns:
            用户友好的错误消息
        """
        error_msg = str(error).lower()

        if 'not found' in error_msg or '404' in error_msg:
            return f"错误：文件 '{file_path}' 未找到"
        elif 'forbidden' in error_msg or '403' in error_msg:
            return f"错误：访问文件 '{file_path}' 被拒绝，请检查权限"
        elif 'unauthorized' in error_msg or '401' in error_msg:
            return "错误：认证失败，请检查用户名和密码"
        elif 'timeout' in error_msg:
            return "错误：连接超时，请检查网络连接和服务器状态"
        elif 'connection' in error_msg:
            return "错误：网络连接失败，请检查网络设置和服务器地址"
        else:
            return f"访问文件时发生错误: {str(error)}"

    def _format_file_size(self, size_bytes: int) -> str:
        """
        格式化文件大小为人类可读的格式

        Args:
            size_bytes: 文件大小（字节）

        Returns:
            格式化后的文件大小字符串
        """
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)

        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1

        return f"{size:.1f} {size_names[i]}"