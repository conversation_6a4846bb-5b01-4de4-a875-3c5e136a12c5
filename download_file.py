# 从 collections.abc 导入 Generator，用于类型提示生成器
from collections.abc import Generator
# 从 typing 导入 Any，用于类型提示
from typing import Any
# 导入 base64，用于对二进制内容进行编码
import base64
# 导入 tempfile，用于创建临时文件
import tempfile
# 导入 os，用于操作系统相关功能，如路径操作和文件删除
import os
# 导入 mimetypes，用于检测文件MIME类型
import mimetypes

# 从 dify_plugin 导入 Tool 基类
from dify_plugin import Tool
# 从 dify_plugin.entities.tool 导入 ToolInvokeMessage，用于创建工具调用消息
from dify_plugin.entities.tool import ToolInvokeMessage


class DownloadFileTool(Tool):
    """
    一个从 NextCloud 下载文件的工具。
    它继承自 dify_plugin.Tool 类。
    """
    
    def _get_file_mime_type(self, file_path: str) -> str:
        """
        根据文件扩展名获取MIME类型
        """
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'
    
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage, None, None]:
        """
        工具的主调用方法，用于从 NextCloud 下载文件。
        :param tool_parameters: 一个包含工具所需参数的字典。
                                - "file_path": (必须) 要下载的文件的路径。
                                - "include_content": (可选) 是否在响应中包含文件内容，默认为 "false"。
        :return: 一个生成器，用于产生工具调用消息（文本、JSON 或 Blob）。
        """
        # 获取工具参数
        file_path = tool_parameters.get("file_path", "").strip()
        include_content = tool_parameters.get("include_content", "false").lower() == "true"
        
        # 验证文件路径
        if not file_path:
            yield self.create_text_message("错误：文件路径不能为空")
            return
            
        # 标准化文件路径
        if not file_path.startswith("/"):
            file_path = "/" + file_path
            
        try:
            # 动态导入 webdavclient3 包
            from webdav3.client import Client
            
            # 从运行时获取 NextCloud 凭据
            webdav_hostname = self.runtime.credentials.get("webdav_hostname")
            username = self.runtime.credentials.get("username")
            app_password = self.runtime.credentials.get("app_password")
            
            # 检查所有凭据是否都已配置
            if not all([webdav_hostname, username, app_password]):
                yield self.create_text_message("错误：NextCloud 凭据配置不完整，请检查 webdav_hostname、username 和 app_password")
                return
            
            # 确保主机名格式正确
            if not webdav_hostname.endswith('/'):
                webdav_hostname += '/'
            if not webdav_hostname.endswith('remote.php/webdav/'):
                webdav_hostname += 'remote.php/webdav/'
            
            # 创建 WebDAV 客户端选项
            webdav_options = {
                'webdav_hostname': webdav_hostname,
                'webdav_login': username,
                'webdav_password': app_password,
                'webdav_timeout': 30  # 添加超时设置
            }
            
            # 创建 WebDAV 客户端实例
            client = Client(webdav_options)
            
            try:
                # 检查文件是否存在
                if not client.check(file_path):
                    yield self.create_text_message(f"错误：文件 '{file_path}' 不存在")
                    return
                
                # 获取文件信息
                file_info = client.info(file_path)
                file_name = os.path.basename(file_path)
                file_size = file_info.get('size', 0)
                
                # 检测文件MIME类型
                content_type = self._get_file_mime_type(file_path)
                
                # 准备响应元数据
                response_data = {
                    "name": file_name,
                    "path": file_path,
                    "size": file_size,
                    "modified": file_info.get('modified', ''),
                    "content_type": content_type
                }
                
                # 如果不需要文件内容，只返回元数据
                if not include_content:
                    summary = f"获取文件信息成功：'{file_name}' (大小: {file_size} 字节)"
                    yield self.create_text_message(summary)
                    yield self.create_json_message(response_data)
                    return
                
                # 检查文件大小限制（例如限制为100MB）
                max_size = 100 * 1024 * 1024  # 100MB
                if file_size > max_size:
                    yield self.create_text_message(f"错误：文件过大 ({file_size} 字节)，超过限制 ({max_size} 字节)")
                    return
                
                # 下载文件内容
                temp_path = None
                try:
                    # 创建临时文件
                    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                        temp_path = temp_file.name
                    
                    # 下载文件到临时路径
                    yield self.create_text_message(f"正在下载文件 '{file_name}'...")
                    client.download_file(file_path, temp_path)
                    
                    # 验证下载的文件大小
                    actual_size = os.path.getsize(temp_path)
                    if actual_size != file_size:
                        yield self.create_text_message(f"警告：下载文件大小不匹配 (期望: {file_size}, 实际: {actual_size})")
                    
                    # 读取文件内容
                    with open(temp_path, 'rb') as f:
                        file_content = f.read()
                    
                    # 验证读取的内容大小
                    if len(file_content) == 0:
                        yield self.create_text_message("错误：下载的文件内容为空")
                        return
                    
                    # 发送成功消息和文件内容
                    summary = f"文件下载成功：'{file_name}' (大小: {len(file_content)} 字节, 类型: {content_type})"
                    yield self.create_text_message(summary)
                    
                    # 创建包含文件内容的 blob 消息
                    yield self.create_blob_message(
                        blob=file_content,
                        meta={
                            "file_name": file_name,
                            "file_path": file_path,
                            "content_type": content_type,
                            "size": len(file_content),
                            "modified": file_info.get('modified', '')
                        }
                    )
                    
                except Exception as download_error:
                    yield self.create_text_message(f"下载文件时发生错误: {str(download_error)}")
                    return
                    
                finally:
                    # 清理临时文件
                    if temp_path and os.path.exists(temp_path):
                        try:
                            os.unlink(temp_path)
                        except Exception as cleanup_error:
                            yield self.create_text_message(f"警告：清理临时文件失败: {str(cleanup_error)}")

            except Exception as webdav_error:
                # 处理 WebDAV 相关错误
                error_msg = str(webdav_error).lower()
                if 'not found' in error_msg or '404' in error_msg:
                    yield self.create_text_message(f"错误：文件 '{file_path}' 未找到")
                elif 'forbidden' in error_msg or '403' in error_msg:
                    yield self.create_text_message(f"错误：访问文件 '{file_path}' 被拒绝，请检查权限")
                elif 'unauthorized' in error_msg or '401' in error_msg:
                    yield self.create_text_message("错误：认证失败，请检查用户名和密码")
                elif 'timeout' in error_msg:
                    yield self.create_text_message("错误：连接超时，请检查网络连接和服务器状态")
                else:
                    yield self.create_text_message(f"访问文件时发生错误: {str(webdav_error)}")
                return
                
        except ImportError:
            # 处理 webdavclient3 未安装的情况
            yield self.create_text_message("错误：缺少必需的依赖库 webdavclient3，请先安装：pip install webdavclient3")
            return
        except Exception as e:
            # 处理其他意外错误
            yield self.create_text_message(f"发生意外错误: {str(e)}")
            return