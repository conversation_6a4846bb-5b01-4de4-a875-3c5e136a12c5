"use client"

import Link from "next/link"
import { getRelatedPosts } from "@/lib/content-manager"
import type { Post } from "@/lib/content-manager"
import { cn } from "@/lib/utils"

interface RelatedPostsProps {
  currentPost: Post
  limit?: number
  className?: string
}

export function RelatedPosts({ currentPost, limit = 3, className = "" }: RelatedPostsProps) {
  const relatedPosts = getRelatedPosts(currentPost, limit)

  if (relatedPosts.length === 0) {
    return null
  }

  return (
    <div className={cn("space-y-6", className)}>
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
        相关推荐
      </h3>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {relatedPosts.map((post) => (
          <RelatedPostCard key={post.slug} post={post} />
        ))}
      </div>
    </div>
  )
}

interface RelatedPostCardProps {
  post: Post
}

function RelatedPostCard({ post }: RelatedPostCardProps) {
  const categoryColors: Record<string, string> = {
    "React": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    "TypeScript": "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    "Next.js": "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200",
    "JavaScript": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    "Web开发": "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    "移动开发": "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
    "云计算": "bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200",
    "数据科学": "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    "人工智能": "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    "DevOps": "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200"
  }

  const difficultyConfig = {
    beginner: { label: "初级", color: "text-green-600 dark:text-green-400", icon: "🟢" },
    intermediate: { label: "中级", color: "text-yellow-600 dark:text-yellow-400", icon: "🟡" },
    advanced: { label: "高级", color: "text-red-600 dark:text-red-400", icon: "🔴" }
  }

  return (
    <Link href={`/posts/${post.slug}`}>
      <article className="group bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200 dark:bg-gray-800 dark:border-gray-700 dark:hover:border-gray-600">
        <div className="p-6">
          {/* Category and Difficulty */}
          <div className="flex items-center justify-between mb-3">
            <span className={cn(
              "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
              categoryColors[post.metadata.category] || "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
            )}>
              {post.metadata.category}
            </span>
            
            {post.metadata.difficulty && (
              <div className="flex items-center space-x-1">
                <span className="text-sm">
                  {difficultyConfig[post.metadata.difficulty as keyof typeof difficultyConfig]?.icon}
                </span>
                <span className={cn(
                  "text-xs font-medium",
                  difficultyConfig[post.metadata.difficulty as keyof typeof difficultyConfig]?.color
                )}>
                  {difficultyConfig[post.metadata.difficulty as keyof typeof difficultyConfig]?.label}
                </span>
              </div>
            )}
          </div>

          {/* Title */}
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-2 line-clamp-2">
            {post.metadata.title}
          </h4>

          {/* Description */}
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
            {post.metadata.description}
          </p>

          {/* Tags */}
          {post.metadata.tags && post.metadata.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-4">
              {post.metadata.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300"
                >
                  {tag}
                </span>
              ))}
              {post.metadata.tags.length > 3 && (
                <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300">
                  +{post.metadata.tags.length - 3}
                </span>
              )}
            </div>
          )}

          {/* Meta Info */}
          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <span className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{post.metadata.readTime || `${post.readingTime}分钟`}</span>
              </span>
              
              <span className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>{new Date(post.metadata.date).toLocaleDateString('zh-CN')}</span>
              </span>
            </div>
            
            <div className="flex items-center text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300">
              <span className="text-sm font-medium">阅读</span>
              <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </article>
    </Link>
  )
}

// Compact version for sidebar
export function RelatedPostsCompact({ currentPost, limit = 5, className = "" }: RelatedPostsProps) {
  const relatedPosts = getRelatedPosts(currentPost, limit)

  if (relatedPosts.length === 0) {
    return null
  }

  return (
    <div className={cn("space-y-4", className)}>
      <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
        相关文章
      </h4>
      
      <div className="space-y-3">
        {relatedPosts.map((post) => (
          <Link
            key={post.slug}
            href={`/posts/${post.slug}`}
            className="block group"
          >
            <article className="p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-200 dark:border-gray-700 dark:hover:border-gray-600 dark:hover:bg-gray-800/50">
              <h5 className="font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-1 line-clamp-2">
                {post.metadata.title}
              </h5>
              
              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded dark:bg-gray-700 dark:text-gray-300">
                  {post.metadata.category}
                </span>
                <span>{post.metadata.readTime || `${post.readingTime}分钟`}</span>
              </div>
            </article>
          </Link>
        ))}
      </div>
    </div>
  )
}
