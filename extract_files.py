import json

data = {
  "output": "{'text': \"Found 6 items in '/zhengju/6.1.1a'\", 'files': [], 'json': [{'items': [{'name': '6.1.1a', 'path': '6.1.1a/', 'type': 'directory'}, {'name': '1.docx', 'path': '1.docx', 'type': 'file'},{'name': '2.docx', 'path': '2.docx', 'type': 'file'}], 'path': '/zhengju/6.1.1a', 'total_count': 6}]}"
}

# The json string is inside the 'output' key, and it's a string representation of a dictionary.
# We need to replace single quotes with double quotes to make it valid JSON.
json_string = data['output'].replace("'", "\"")

# Now, we can parse the string to get the dictionary
output_data = json.loads(json_string)

# Access the list of items
items = output_data['json'][0]['items']

# Extract the names of files
file_names = [item['name'] for item in items if item['type'] == 'file']

# Print the result
for name in file_names:
    print(name)
