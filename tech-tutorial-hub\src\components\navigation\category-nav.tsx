"use client"

import Link from "next/link"
import { usePathname, useSearchParams } from "next/navigation"
import { getAllCategories } from "@/lib/content-manager"
import { cn } from "@/lib/utils"

interface CategoryNavProps {
  className?: string
  showAll?: boolean
}

export function CategoryNav({ className = "", showAll = true }: CategoryNavProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const currentCategory = searchParams.get("category")
  
  const categories = getAllCategories()

  const categoryIcons: Record<string, string> = {
    "React": "⚛️",
    "TypeScript": "🔷",
    "Next.js": "▲",
    "JavaScript": "🟨",
    "Web开发": "🌐",
    "移动开发": "📱",
    "云计算": "☁️",
    "数据科学": "📊",
    "人工智能": "🤖",
    "DevOps": "🔧",
    "前端开发": "💻",
    "后端开发": "⚙️",
    "全栈开发": "🚀"
  }

  return (
    <div className={cn("space-y-4", className)}>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
        分类导航
      </h3>
      
      <div className="space-y-2">
        {showAll && (
          <Link
            href="/tutorials"
            className={cn(
              "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors",
              !currentCategory
                ? "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300"
                : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
            )}
          >
            <span className="text-lg">📚</span>
            <div className="flex-1">
              <span className="font-medium">全部教程</span>
              <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                ({categories.reduce((sum, cat) => sum + cat.count, 0)})
              </span>
            </div>
          </Link>
        )}

        {categories.map((category) => (
          <Link
            key={category.name}
            href={`/tutorials?category=${encodeURIComponent(category.name)}`}
            className={cn(
              "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors",
              currentCategory === category.name
                ? "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300"
                : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
            )}
          >
            <span className="text-lg">
              {categoryIcons[category.name] || "📁"}
            </span>
            <div className="flex-1">
              <span className="font-medium">{category.name}</span>
              <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                ({category.count})
              </span>
            </div>
          </Link>
        ))}
      </div>
    </div>
  )
}

// Compact version for mobile or sidebar
export function CategoryNavCompact({ className = "" }: { className?: string }) {
  const searchParams = useSearchParams()
  const currentCategory = searchParams.get("category")
  
  const categories = getAllCategories()

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      <Link
        href="/tutorials"
        className={cn(
          "px-3 py-1 rounded-full text-sm font-medium transition-colors",
          !currentCategory
            ? "bg-blue-600 text-white"
            : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
        )}
      >
        全部
      </Link>
      
      {categories.slice(0, 6).map((category) => (
        <Link
          key={category.name}
          href={`/tutorials?category=${encodeURIComponent(category.name)}`}
          className={cn(
            "px-3 py-1 rounded-full text-sm font-medium transition-colors",
            currentCategory === category.name
              ? "bg-blue-600 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
          )}
        >
          {category.name}
          <span className="ml-1 text-xs opacity-75">
            {category.count}
          </span>
        </Link>
      ))}
      
      {categories.length > 6 && (
        <Link
          href="/categories"
          className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
        >
          更多...
        </Link>
      )}
    </div>
  )
}
