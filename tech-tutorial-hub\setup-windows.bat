@echo off
echo ========================================
echo Tech Tutorial Hub - Windows Setup
echo ========================================

echo.
echo 1. Checking Node.js installation...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo 2. Checking npm installation...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    pause
    exit /b 1
)

echo.
echo 3. Cleaning previous installation...
if exist node_modules (
    echo Removing existing node_modules...
    rmdir /s /q node_modules
)
if exist package-lock.json (
    echo Removing package-lock.json...
    del package-lock.json
)

echo.
echo 4. Installing dependencies...
echo This may take a few minutes...
npm install --verbose

if %errorlevel% neq 0 (
    echo.
    echo ERROR: npm install failed
    echo Trying with --legacy-peer-deps flag...
    npm install --legacy-peer-deps --verbose
)

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Installation still failed
    echo Trying with yarn...
    npm install -g yarn
    yarn install
)

echo.
echo 5. Verifying installation...
if exist node_modules\.bin\next.cmd (
    echo ✓ Next.js installed successfully
) else (
    echo ✗ Next.js installation failed
    pause
    exit /b 1
)

echo.
echo 6. Starting development server...
echo Opening http://localhost:3000 in your browser...
start http://localhost:3000
npm run dev

pause
