import { trackEvent } from './analytics'

// Web Vitals 性能指标跟踪
export interface WebVitalsMetric {
  name: string
  value: number
  id: string
  delta: number
  entries: PerformanceEntry[]
}

// 性能指标阈值
const PERFORMANCE_THRESHOLDS = {
  CLS: { good: 0.1, poor: 0.25 },
  FID: { good: 100, poor: 300 },
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  TTFB: { good: 800, poor: 1800 }
}

// 获取性能评级
function getPerformanceRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const thresholds = PERFORMANCE_THRESHOLDS[name as keyof typeof PERFORMANCE_THRESHOLDS]
  if (!thresholds) return 'good'
  
  if (value <= thresholds.good) return 'good'
  if (value <= thresholds.poor) return 'needs-improvement'
  return 'poor'
}

// 发送性能指标到 Google Analytics
export function sendToAnalytics(metric: WebVitalsMetric) {
  const rating = getPerformanceRating(metric.name, metric.value)
  
  trackEvent.performance(metric.name, metric.value, window.location.pathname)
  
  // 发送详细的性能数据
  trackEvent.event({
    action: 'web_vitals',
    category: 'performance',
    label: metric.name,
    value: Math.round(metric.value),
    custom_parameters: {
      metric_id: metric.id,
      metric_value: metric.value,
      metric_delta: metric.delta,
      metric_rating: rating,
      page_path: window.location.pathname
    }
  })
}

// 监控页面加载性能
export function monitorPagePerformance() {
  if (typeof window === 'undefined') return

  // 监控页面加载时间
  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      if (navigation) {
        const metrics = {
          dns: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcp: navigation.connectEnd - navigation.connectStart,
          ttfb: navigation.responseStart - navigation.requestStart,
          download: navigation.responseEnd - navigation.responseStart,
          domParse: navigation.domContentLoadedEventEnd - navigation.responseEnd,
          domReady: navigation.domContentLoadedEventEnd - navigation.navigationStart,
          pageLoad: navigation.loadEventEnd - navigation.navigationStart
        }

        // 发送各项性能指标
        Object.entries(metrics).forEach(([name, value]) => {
          if (value > 0) {
            trackEvent.performance(name, value, window.location.pathname)
          }
        })
      }
    }, 0)
  })
}

// 监控资源加载性能
export function monitorResourcePerformance() {
  if (typeof window === 'undefined') return

  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === 'resource') {
        const resource = entry as PerformanceResourceTiming
        
        // 跟踪慢加载的资源
        if (resource.duration > 1000) {
          trackEvent.event({
            action: 'slow_resource',
            category: 'performance',
            label: resource.name,
            value: Math.round(resource.duration),
            custom_parameters: {
              resource_type: resource.initiatorType,
              resource_size: resource.transferSize || 0,
              resource_duration: resource.duration
            }
          })
        }
      }
    })
  })

  observer.observe({ entryTypes: ['resource'] })
}

// 监控长任务
export function monitorLongTasks() {
  if (typeof window === 'undefined') return

  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === 'longtask') {
        trackEvent.event({
          action: 'long_task',
          category: 'performance',
          label: 'main_thread_blocking',
          value: Math.round(entry.duration),
          custom_parameters: {
            task_duration: entry.duration,
            start_time: entry.startTime
          }
        })
      }
    })
  })

  try {
    observer.observe({ entryTypes: ['longtask'] })
  } catch (e) {
    // Long Task API 不支持时忽略
  }
}

// 监控内存使用情况
export function monitorMemoryUsage() {
  if (typeof window === 'undefined' || !('memory' in performance)) return

  const memory = (performance as any).memory
  
  if (memory) {
    const memoryInfo = {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      limit: memory.jsHeapSizeLimit
    }

    // 如果内存使用超过80%，发送警告
    const usagePercent = (memoryInfo.used / memoryInfo.limit) * 100
    
    if (usagePercent > 80) {
      trackEvent.event({
        action: 'high_memory_usage',
        category: 'performance',
        label: 'memory_warning',
        value: Math.round(usagePercent),
        custom_parameters: {
          memory_used: memoryInfo.used,
          memory_total: memoryInfo.total,
          memory_limit: memoryInfo.limit,
          usage_percent: usagePercent
        }
      })
    }
  }
}

// 监控网络连接质量
export function monitorNetworkQuality() {
  if (typeof window === 'undefined' || !('connection' in navigator)) return

  const connection = (navigator as any).connection
  
  if (connection) {
    trackEvent.event({
      action: 'network_info',
      category: 'performance',
      label: connection.effectiveType || 'unknown',
      custom_parameters: {
        connection_type: connection.type || 'unknown',
        effective_type: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0,
        save_data: connection.saveData || false
      }
    })

    // 监听网络变化
    connection.addEventListener('change', () => {
      trackEvent.event({
        action: 'network_change',
        category: 'performance',
        label: connection.effectiveType || 'unknown',
        custom_parameters: {
          new_connection_type: connection.type || 'unknown',
          new_effective_type: connection.effectiveType || 'unknown'
        }
      })
    })
  }
}

// 监控错误
export function monitorErrors() {
  if (typeof window === 'undefined') return

  // JavaScript 错误
  window.addEventListener('error', (event) => {
    trackEvent.error('javascript_error', event.message, window.location.pathname)
  })

  // Promise 拒绝错误
  window.addEventListener('unhandledrejection', (event) => {
    trackEvent.error('promise_rejection', event.reason?.toString() || 'Unknown promise rejection', window.location.pathname)
  })
}

// 初始化所有性能监控
export function initPerformanceMonitoring() {
  if (typeof window === 'undefined') return

  monitorPagePerformance()
  monitorResourcePerformance()
  monitorLongTasks()
  monitorMemoryUsage()
  monitorNetworkQuality()
  monitorErrors()

  // 定期检查内存使用情况
  setInterval(monitorMemoryUsage, 30000) // 每30秒检查一次
}

// 手动发送性能报告
export function sendPerformanceReport() {
  if (typeof window === 'undefined') return

  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  
  if (navigation) {
    const report = {
      url: window.location.href,
      timestamp: new Date().toISOString(),
      metrics: {
        ttfb: navigation.responseStart - navigation.requestStart,
        domReady: navigation.domContentLoadedEventEnd - navigation.navigationStart,
        pageLoad: navigation.loadEventEnd - navigation.navigationStart,
        resources: performance.getEntriesByType('resource').length
      }
    }

    trackEvent.event({
      action: 'performance_report',
      category: 'performance',
      label: 'manual_report',
      custom_parameters: report
    })
  }
}
