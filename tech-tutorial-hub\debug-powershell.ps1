# PowerShell Debug Script for Tech Tutorial Hub
Write-Host "=== Tech Tutorial Hub PowerShell Debug ===" -ForegroundColor Cyan

# 1. Check current directory
Write-Host "`n1. Current Directory:" -ForegroundColor Yellow
Get-Location

# 2. List project files
Write-Host "`n2. Project Files:" -ForegroundColor Yellow
Get-ChildItem -Name | Sort-Object

# 3. Check Node.js
Write-Host "`n3. Node.js Version:" -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Node.js not found" -ForegroundColor Red
}

# 4. Check npm
Write-Host "`n4. npm Version:" -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "✓ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ npm not found" -ForegroundColor Red
}

# 5. Check package.json
Write-Host "`n5. Package.json:" -ForegroundColor Yellow
if (Test-Path "package.json") {
    Write-Host "✓ package.json exists" -ForegroundColor Green
    $packageContent = Get-Content "package.json" | ConvertFrom-Json
    Write-Host "   Name: $($packageContent.name)" -ForegroundColor Gray
    Write-Host "   Version: $($packageContent.version)" -ForegroundColor Gray
} else {
    Write-Host "✗ package.json not found" -ForegroundColor Red
}

# 6. Check node_modules
Write-Host "`n6. Dependencies:" -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "✓ node_modules exists" -ForegroundColor Green
    $nodeModulesSize = (Get-ChildItem "node_modules" -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
    Write-Host "   Size: $([math]::Round($nodeModulesSize, 2)) MB" -ForegroundColor Gray
} else {
    Write-Host "✗ node_modules not found" -ForegroundColor Red
}

# 7. Check Next.js
Write-Host "`n7. Next.js Installation:" -ForegroundColor Yellow
if (Test-Path "node_modules\next") {
    Write-Host "✓ Next.js installed" -ForegroundColor Green
    if (Test-Path "node_modules\.bin\next.cmd") {
        Write-Host "✓ Next.js binary found" -ForegroundColor Green
    } else {
        Write-Host "⚠ Next.js binary missing" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ Next.js not installed" -ForegroundColor Red
}

# 8. Check TypeScript
Write-Host "`n8. TypeScript Configuration:" -ForegroundColor Yellow
if (Test-Path "tsconfig.json") {
    Write-Host "✓ tsconfig.json exists" -ForegroundColor Green
} else {
    Write-Host "✗ tsconfig.json not found" -ForegroundColor Red
}

# 9. Check source files
Write-Host "`n9. Source Files:" -ForegroundColor Yellow
$sourceFiles = @("src\app\layout.tsx", "src\app\page.tsx", "src\components\ui\button.tsx")
foreach ($file in $sourceFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        Write-Host "✗ $file" -ForegroundColor Red
    }
}

# 10. Check for running processes
Write-Host "`n10. Running Node Processes:" -ForegroundColor Yellow
$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
if ($nodeProcesses) {
    Write-Host "✓ Found $($nodeProcesses.Count) Node.js process(es)" -ForegroundColor Green
    foreach ($proc in $nodeProcesses) {
        Write-Host "   PID: $($proc.Id) - $($proc.ProcessName)" -ForegroundColor Gray
    }
} else {
    Write-Host "○ No Node.js processes running" -ForegroundColor Gray
}

Write-Host "`n=== Debug Complete ===" -ForegroundColor Cyan
Write-Host "Ready for next steps!" -ForegroundColor Green
